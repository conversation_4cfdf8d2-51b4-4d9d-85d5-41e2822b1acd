"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/vessel-location-display */ \"(app-pages-browser)/./src/components/ui/vessel-location-display.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ var _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp, vessel data, and vessels list)\nconst createMaintenanceReportColumns = function(bp, getVesselWithIcon) {\n    let vessels = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    return (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, _this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 21\n                }, _this);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col py-2.5 gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"desktop:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 37\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 33\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 41\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 37\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 29\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 25\n                        }, _this),\n                        item.dueDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"tablet-sm:hidden\",\n                            children: [\n                                \"Due date:\",\n                                \" \",\n                                dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 29\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden desktop:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: taskContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 33\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 29\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 25\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 21\n                }, _this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 29\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Find the actual vessel by name from the vessels list\n                const actualVessel = vessels.find((vessel)=>vessel.title === item.vesselName);\n                if (actualVessel) {\n                    // Use the actual vessel data with proper ID\n                    const vesselWithIcon = getVesselWithIcon(actualVessel.id, actualVessel);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        vesselId: actualVessel.id,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 25\n                    }, _this);\n                } else {\n                    // Fallback for vessels not found in the list\n                    const vesselForIcon = {\n                        id: 0,\n                        title: item.vesselName\n                    };\n                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_vessel_location_display__WEBPACK_IMPORTED_MODULE_17__.VesselLocationDisplay, {\n                        vessel: vesselWithIcon,\n                        displayText: item.vesselName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 25\n                    }, _this);\n                }\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"desktop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        mobileClickable: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                mobileClickable: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    size: \"sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 41\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 37\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 33\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 33\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 17\n                }, _this);\n            },\n            breakpoint: \"laptop\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 17\n                }, _this);\n            },\n            cellClassName: \"px-2.5\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 28\n                    }, _this);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 33\n                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 29\n                    }, _this)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n};\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filterIsOverDue, setFilterIsOverDue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load vessels for vessel lookup by name\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_18__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                const activeVessels = queryVesselResponse.readVessels.nodes.filter((vessel)=>!vessel.archived);\n                setVessels(activeVessels);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    // Load vessels on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0\n            }\n        });\n    }, [\n        queryVessels\n    ]);\n    // Create columns with access to bp, vessel icon data, and vessels list\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon, vessels);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            // Log unique status values to understand what's available\n            const statusValues = new Set();\n            data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.forEach((node)=>{\n                if (node.status) {\n                    statusValues.add(node.status);\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                // Handle both single vessel and multi-vessel selection\n                if (Array.isArray(data)) {\n                    setSelectedVessels(data);\n                } else if (data) {\n                    // Single vessel selection - convert to array for consistency\n                    setSelectedVessels([\n                        data\n                    ]);\n                } else {\n                    // Clear selection\n                    setSelectedVessels([]);\n                }\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels && selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>+item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: +crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    console.log(\"reportData\", reportData);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 680,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    noDataText: \"No maintenance data found, try clicking generate report to view results\",\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 689,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"7Gqguj1UpgV+b70yFfkPzfighTc=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});