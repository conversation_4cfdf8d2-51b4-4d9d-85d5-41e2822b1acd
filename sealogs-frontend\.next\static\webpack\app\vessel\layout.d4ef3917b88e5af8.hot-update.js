"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/layout",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: function() { return /* binding */ AppSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_team__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/team */ \"(app-pages-browser)/./src/components/team.tsx\");\n/* harmony import */ var _nav_main__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _nav_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _nav_single_links__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./nav-single-links */ \"(app-pages-browser)/./src/components/nav-single-links.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _app_lib_icons_SealogsVesselsSelectedIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/icons/SealogsVesselsSelectedIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsVesselsSelectedIcon.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_icons_SealogsCrewSelectedIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/lib/icons/SealogsCrewSelectedIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsCrewSelectedIcon.ts\");\n/* harmony import */ var _app_lib_icons_SealogsHealthSafetySelectedIcon__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/lib/icons/SealogsHealthSafetySelectedIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsHealthSafetySelectedIcon.ts\");\n/* harmony import */ var _app_lib_icons_SealogsInventorySelectedIcon__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/icons/SealogsInventorySelectedIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsInventorySelectedIcon.ts\");\n/* harmony import */ var _app_lib_icons_SealogsDashboardSelectedIcon__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/lib/icons/SealogsDashboardSelectedIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsDashboardSelectedIcon.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//logo versions: logo-small-dark.png | logo-small.png\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    _s();\n    const [clientTitle, setClientTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loggedUserName, setLoggedUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loggedEmail, setLoggedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.usePathname)();\n    console.log(loggedEmail);\n    const NavData = {\n        user: {\n            name: \"SeaLogs\",\n            email: loggedEmail\n        },\n        team: {\n            name: \"South Inc\",\n            logo: \"logo-small.png\",\n            plan: \"powered by SeaLogs\"\n        },\n        versions: [\n            \"3.4.0\",\n            \"1.1.0-alpha\",\n            \"2.0.0-beta1\"\n        ],\n        singleLinks: [\n            {\n                name: \"Home port\",\n                url: \"/dashboard\",\n                icon: pathname === \"/dashboard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsDashboardSelectedIcon__WEBPACK_IMPORTED_MODULE_13__.SealogsDashboardSelectedIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 52\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsDashboardIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 107\n                }, this)\n            },\n            {\n                name: \"All vessels\",\n                url: \"/vessel\",\n                icon: pathname === \"/vessel\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsVesselsSelectedIcon__WEBPACK_IMPORTED_MODULE_8__.SealogsVesselsSelectedIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 49\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsVesselsIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 102\n                }, this)\n            }\n        ],\n        navMain: [\n            {\n                title: \"Crew\",\n                url: \"#\",\n                icon: pathname === \"/crew\" || pathname === \"/crew-training\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsCrewSelectedIcon__WEBPACK_IMPORTED_MODULE_10__.SealogsCrewSelectedIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 82\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsCrewIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 131\n                }, this),\n                items: [\n                    {\n                        title: \"All crew\",\n                        url: \"/crew\"\n                    },\n                    {\n                        title: \"Training / Drills\",\n                        url: \"/crew-training\"\n                    }\n                ]\n            },\n            {\n                title: \"Health & safety\",\n                url: \"#\",\n                icon: pathname === \"/risk-evaluations\" || pathname === \"/risk-strategies\" || pathname === \"/training-matrix\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsHealthSafetySelectedIcon__WEBPACK_IMPORTED_MODULE_11__.SealogsHealthSafetySelectedIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 131\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsHealthSafetyIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 189\n                }, this),\n                items: [\n                    {\n                        title: \"Risk Evaluations\",\n                        url: \"/risk-evaluations\"\n                    },\n                    {\n                        title: \"Risk Strategies\",\n                        url: \"/risk-strategies\"\n                    },\n                    {\n                        title: \"Drills / training matrix\",\n                        url: \"/training-matrix\"\n                    }\n                ]\n            }\n        ],\n        navMain2: [\n            {\n                title: \"Inventory\",\n                url: \"#\",\n                icon: pathname === \"/inventory\" || pathname === \"/inventory/suppliers\" || pathname === \"/maintenance\" || pathname === \"/document-locker\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsInventorySelectedIcon__WEBPACK_IMPORTED_MODULE_12__.SealogsInventorySelectedIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 159\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_7__.SealogsInventoryIcon, {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 214\n                }, this),\n                items: [\n                    {\n                        title: \"All inventory\",\n                        url: \"/inventory\"\n                    },\n                    {\n                        title: \"Suppliers\",\n                        url: \"/inventory/suppliers\"\n                    },\n                    {\n                        title: \"Maintenance\",\n                        url: \"/maintenance\"\n                    },\n                    {\n                        title: \"Documents\",\n                        url: \"/document-locker\"\n                    }\n                ]\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setClientTitle(localStorage.getItem(\"clientTitle\") || \"\");\n            // init()\n            setIsLoading(false);\n        }\n        if (true) {\n            var _localStorage_getItem, _localStorage_getItem1;\n            setLoggedUserName(\"\".concat((_localStorage_getItem = localStorage.getItem(\"firstName\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : \"\", \" \").concat((_localStorage_getItem1 = localStorage.getItem(\"surname\")) !== null && _localStorage_getItem1 !== void 0 ? _localStorage_getItem1 : \"\"));\n            const user = localStorage.getItem(\"user\");\n            if (user !== null) {\n                var _JSON_parse_email;\n                setLoggedEmail(\"\".concat((_JSON_parse_email = JSON.parse(user).email) !== null && _JSON_parse_email !== void 0 ? _JSON_parse_email : \"\"));\n            }\n        }\n    }, [\n        isLoading\n    ]);\n    // Memoize the team data to prevent unnecessary re-renders\n    const teamData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>({\n            ...NavData.team,\n            name: clientTitle || NavData.team.name\n        }), [\n        clientTitle\n    ]);\n    // Memoize the user data to prevent unnecessary re-renders\n    const userData = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(()=>({\n            ...NavData.user,\n            name: loggedUserName || NavData.user.name,\n            email: loggedEmail || NavData.user.email\n        }), [\n        loggedUserName,\n        loggedEmail\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n        collapsible: \"icon\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_team__WEBPACK_IMPORTED_MODULE_3__.Team, {\n                    team: teamData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 165,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_single_links__WEBPACK_IMPORTED_MODULE_6__.NavSingle, {\n                        projects: NavData.singleLinks\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_main__WEBPACK_IMPORTED_MODULE_4__.NavMain, {\n                        items: NavData.navMain\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_main__WEBPACK_IMPORTED_MODULE_4__.NavMain, {\n                        items: NavData.navMain2\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 168,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarFooter, {\n                className: \"pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav_user__WEBPACK_IMPORTED_MODULE_5__.NavUser, {\n                    user: userData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 173,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarRail, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 176,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 164,\n        columnNumber: 9\n    }, this);\n}\n_s(AppSidebar, \"wZH9eu8q7ej8lHiKXpJw/U6tRXo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});