"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/daily-checks/engine-checks/pre-startup-checks/electrical-fields.tsx":
/*!****************************************************************************************!*\
  !*** ./src/app/ui/daily-checks/engine-checks/pre-startup-checks/electrical-fields.tsx ***!
  \****************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ElectricalFields; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _use_engine_fields__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../use-engine-fields */ \"(app-pages-browser)/./src/app/ui/daily-checks/engine-checks/use-engine-fields.tsx\");\n/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../actions */ \"(app-pages-browser)/./src/app/ui/daily-checks/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ElectricalFields(param) {\n    let { logBookConfig, vesselDailyCheck, locked, edit_logBookEntry, setOpenDescriptionPanel, setDescriptionPanelHeading, setDescriptionPanelContent, handleEngineChecks, getComment, showCommentPopup, fieldImages, refreshImages } = param;\n    var _getComment, _getComment1;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const { preElectricalFields, preElectricalVisualFields } = (0,_use_engine_fields__WEBPACK_IMPORTED_MODULE_2__.useEngineFields)(logBookConfig, vesselDailyCheck);\n    const shouldDisplay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _getFilteredFields, _getFilteredFields1;\n        return ((_getFilteredFields = (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFilteredFields)(preElectricalFields, true, logBookConfig)) === null || _getFilteredFields === void 0 ? void 0 : _getFilteredFields.filter((groupField)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(groupField.name, logBookConfig)).length) > 0 || ((_getFilteredFields1 = (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFilteredFields)(preElectricalVisualFields, true, logBookConfig)) === null || _getFilteredFields1 === void 0 ? void 0 : _getFilteredFields1.filter((groupField)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(groupField.name, logBookConfig)).length) > 0 || (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(\"Generator\", logBookConfig) || (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(\"ShorePower\", logBookConfig);\n    }, [\n        logBookConfig,\n        preElectricalFields,\n        preElectricalVisualFields\n    ]);\n    const filteredPreElectricalFields = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _getFilteredFields;\n        var _getFilteredFields_filter;\n        return (_getFilteredFields_filter = (_getFilteredFields = (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFilteredFields)(preElectricalFields, true, logBookConfig)) === null || _getFilteredFields === void 0 ? void 0 : _getFilteredFields.filter((groupField)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(groupField.name, logBookConfig))) !== null && _getFilteredFields_filter !== void 0 ? _getFilteredFields_filter : [];\n    }, [\n        preElectricalFields,\n        logBookConfig\n    ]);\n    const filteredPreElectricalVisualFields = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _getFilteredFields;\n        var _getFilteredFields_filter;\n        return (_getFilteredFields_filter = (_getFilteredFields = (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFilteredFields)(preElectricalVisualFields, true, logBookConfig)) === null || _getFilteredFields === void 0 ? void 0 : _getFilteredFields.filter((groupField)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(groupField.name, logBookConfig))) !== null && _getFilteredFields_filter !== void 0 ? _getFilteredFields_filter : [];\n    }, [\n        preElectricalVisualFields,\n        logBookConfig\n    ]);\n    if (!shouldDisplay) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: logBookConfig && !vesselDailyCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                (filteredPreElectricalFields.filter((groupField)=>{\n                    var _groupField_items;\n                    return groupField === null || groupField === void 0 ? void 0 : (_groupField_items = groupField.items) === null || _groupField_items === void 0 ? void 0 : _groupField_items.some((field)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig));\n                }).length > 0 || filteredPreElectricalVisualFields.filter((groupField)=>{\n                    var _groupField_items;\n                    return groupField === null || groupField === void 0 ? void 0 : (_groupField_items = groupField.items) === null || _groupField_items === void 0 ? void 0 : _groupField_items.some((field)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig));\n                }).length > 0 || (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(\"Generator\", logBookConfig) || (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(\"ShorePower\", logBookConfig)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__.CheckFieldTopContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 25\n                }, this),\n                filteredPreElectricalFields.map((groupField)=>{\n                    var _groupField_items_filter, _groupField_items;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            groupField === null || groupField === void 0 ? void 0 : (_groupField_items = groupField.items) === null || _groupField_items === void 0 ? void 0 : (_groupField_items_filter = _groupField_items.filter((field)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig))) === null || _groupField_items_filter === void 0 ? void 0 : _groupField_items_filter.map((field, index)=>{\n                                var _getComment;\n                                return(// <span\n                                //     key={`${field.label}-${index}`}\n                                //     className=\" lg:\">\n                                //     {index <\n                                //     groupField\n                                //         .items\n                                //         .length -\n                                //         1\n                                //         ? field.label +\n                                //           ' -'\n                                //         : field.label}\n                                //     {displayDescription(\n                                //         field.name,\n                                //         logBookConfig,\n                                //     ) && (\n                                //         <SeaLogsButton\n                                //             icon=\"alert\"\n                                //             className=\"w-6 h-6 sup -mt-2 ml-0.5\"\n                                //             action={() => {\n                                //                 setDescriptionPanelContent(\n                                //                     displayDescription(\n                                //                         field.name,\n                                //                         logBookConfig,\n                                //                     ),\n                                //                 )\n                                //                 setOpenDescriptionPanel(\n                                //                     true,\n                                //                 )\n                                //                 setDescriptionPanelHeading(\n                                //                     field.name,\n                                //                 )\n                                //             }}\n                                //         />\n                                //     )}{' '}\n                                // </span>\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__.DailyCheckField, {\n                                    locked: locked || !edit_logBookEntry,\n                                    displayField: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig),\n                                    displayDescription: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(field.name, logBookConfig),\n                                    setOpenDescriptionPanel: setOpenDescriptionPanel,\n                                    setDescriptionPanelHeading: setDescriptionPanelHeading,\n                                    displayLabel: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFieldLabel)(field.name, logBookConfig),\n                                    inputId: field.value,\n                                    handleNoChange: ()=>// field.handleChange(\n                                        //     false,\n                                        // )\n                                        handleEngineChecks(false, field.value),\n                                    defaultNoChecked: field.checked === \"Not_Ok\",\n                                    handleYesChange: ()=>// field.handleChange(\n                                        //     true,\n                                        // )\n                                        handleEngineChecks(true, field.value),\n                                    defaultYesChecked: field.checked === \"Ok\",\n                                    commentAction: ()=>showCommentPopup(getComment(field.name), (0,_actions__WEBPACK_IMPORTED_MODULE_3__.composeField)(field.name, logBookConfig)),\n                                    comment: (_getComment = getComment(field.name)) === null || _getComment === void 0 ? void 0 : _getComment.comment,\n                                    displayImage: true,\n                                    fieldImages: fieldImages,\n                                    onImageUpload: refreshImages\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 37\n                                }, this));\n                            }),\n                            (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(groupField.name, logBookConfig) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"text\",\n                                iconLeft: _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                onClick: ()=>{\n                                    setDescriptionPanelContent((0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(groupField.name, logBookConfig));\n                                    setOpenDescriptionPanel(true);\n                                    setDescriptionPanelHeading(groupField.name);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 33\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                        ]\n                    }, groupField.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 25\n                    }, this);\n                }),\n                filteredPreElectricalVisualFields.map((groupField)=>{\n                    var _groupField_items_filter, _groupField_items;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            groupField === null || groupField === void 0 ? void 0 : (_groupField_items = groupField.items) === null || _groupField_items === void 0 ? void 0 : (_groupField_items_filter = _groupField_items.filter((field)=>(0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig))) === null || _groupField_items_filter === void 0 ? void 0 : _groupField_items_filter.map((field, index)=>{\n                                var _getComment;\n                                return(// <span\n                                //     key={`${field.label}-${index}`}\n                                //     className=\" lg:\">\n                                //     {index <\n                                //     groupField\n                                //         .items\n                                //         .length -\n                                //         1\n                                //         ? field.label +\n                                //           ' -'\n                                //         : field.label}\n                                //     {displayDescription(\n                                //         field.name,\n                                //         logBookConfig,\n                                //     ) && (\n                                //         <SeaLogsButton\n                                //             icon=\"alert\"\n                                //             className=\"w-6 h-6 sup -mt-2 ml-0.5\"\n                                //             action={() => {\n                                //                 setDescriptionPanelContent(\n                                //                     displayDescription(\n                                //                         field.name,\n                                //                         logBookConfig,\n                                //                     ),\n                                //                 )\n                                //                 setOpenDescriptionPanel(\n                                //                     true,\n                                //                 )\n                                //                 setDescriptionPanelHeading(\n                                //                     field.name,\n                                //                 )\n                                //             }}\n                                //         />\n                                //     )}{' '}\n                                // </span>\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__.DailyCheckField, {\n                                    locked: locked || !edit_logBookEntry,\n                                    displayField: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(field.name, logBookConfig),\n                                    displayDescription: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(field.name, logBookConfig),\n                                    setOpenDescriptionPanel: setOpenDescriptionPanel,\n                                    setDescriptionPanelHeading: setDescriptionPanelHeading,\n                                    displayLabel: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFieldLabel)(field.name, logBookConfig),\n                                    inputId: field.value,\n                                    handleNoChange: ()=>// field.handleChange(\n                                        //     false,\n                                        // )\n                                        handleEngineChecks(false, field.value),\n                                    defaultNoChecked: field.checked === \"Not_Ok\",\n                                    handleYesChange: ()=>// field.handleChange(\n                                        //     true,\n                                        // )\n                                        handleEngineChecks(true, field.value),\n                                    defaultYesChecked: field.checked === \"Ok\",\n                                    commentAction: ()=>showCommentPopup(getComment(field.name), (0,_actions__WEBPACK_IMPORTED_MODULE_3__.composeField)(field.name, logBookConfig)),\n                                    comment: (_getComment = getComment(field.name)) === null || _getComment === void 0 ? void 0 : _getComment.comment,\n                                    displayImage: true,\n                                    fieldImages: fieldImages,\n                                    onImageUpload: refreshImages\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 41\n                                }, this));\n                            }),\n                            (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(groupField.name, logBookConfig) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"text\",\n                                iconLeft: _barrel_optimize_names_AlertCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                onClick: ()=>{\n                                    setDescriptionPanelContent((0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(groupField.name, logBookConfig));\n                                    setOpenDescriptionPanel(true);\n                                    setDescriptionPanelHeading(groupField.name);\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 37\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                        ]\n                    }, groupField.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 29\n                    }, this);\n                }),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__.DailyCheckField, {\n                    locked: locked || !edit_logBookEntry,\n                    displayField: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(\"Generator\", logBookConfig),\n                    displayDescription: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(\"Generator\", logBookConfig),\n                    setOpenDescriptionPanel: setOpenDescriptionPanel,\n                    setDescriptionPanelHeading: setDescriptionPanelHeading,\n                    displayLabel: // 'Generator is working as expected'\n                    (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFieldLabel)(\"Generator\", logBookConfig),\n                    className: \"flex\",\n                    inputId: \"generator\",\n                    handleNoChange: ()=>handleEngineChecks(false, \"generator\"),\n                    defaultNoChecked: (vesselDailyCheck === null || vesselDailyCheck === void 0 ? void 0 : vesselDailyCheck.generator) === \"Not_Ok\",\n                    handleYesChange: ()=>handleEngineChecks(true, \"generator\"),\n                    defaultYesChecked: (vesselDailyCheck === null || vesselDailyCheck === void 0 ? void 0 : vesselDailyCheck.generator) === \"Ok\",\n                    commentAction: ()=>showCommentPopup(getComment(\"Generator\"), (0,_actions__WEBPACK_IMPORTED_MODULE_3__.composeField)(\"Generator\", logBookConfig)),\n                    comment: (_getComment = getComment(\"Generator\")) === null || _getComment === void 0 ? void 0 : _getComment.comment,\n                    displayImage: true,\n                    fieldImages: fieldImages,\n                    onImageUpload: refreshImages\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 21\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_5__.DailyCheckField, {\n                    locked: locked || !edit_logBookEntry,\n                    displayField: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayField)(\"ShorePower\", logBookConfig),\n                    displayDescription: (0,_actions__WEBPACK_IMPORTED_MODULE_3__.displayDescription)(\"ShorePower\", logBookConfig),\n                    setOpenDescriptionPanel: setOpenDescriptionPanel,\n                    setDescriptionPanelHeading: setDescriptionPanelHeading,\n                    displayLabel: // 'Shore power is disconnected'\n                    (0,_actions__WEBPACK_IMPORTED_MODULE_3__.getFieldLabel)(\"ShorePower\", logBookConfig),\n                    className: \"flex\",\n                    inputId: \"shorePower\",\n                    handleNoChange: ()=>handleEngineChecks(false, \"shorePower\"),\n                    defaultNoChecked: (vesselDailyCheck === null || vesselDailyCheck === void 0 ? void 0 : vesselDailyCheck.shorePower) === \"Not_Ok\",\n                    handleYesChange: ()=>handleEngineChecks(true, \"shorePower\"),\n                    defaultYesChecked: (vesselDailyCheck === null || vesselDailyCheck === void 0 ? void 0 : vesselDailyCheck.shorePower) === \"Ok\",\n                    commentAction: ()=>showCommentPopup(getComment(\"ShorePower\"), (0,_actions__WEBPACK_IMPORTED_MODULE_3__.composeField)(\"ShorePower\", logBookConfig)),\n                    comment: (_getComment1 = getComment(\"ShorePower\")) === null || _getComment1 === void 0 ? void 0 : _getComment1.comment,\n                    displayImage: true,\n                    fieldImages: fieldImages,\n                    onImageUpload: refreshImages\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\daily-checks\\\\engine-checks\\\\pre-startup-checks\\\\electrical-fields.tsx\",\n                    lineNumber: 729,\n                    columnNumber: 21\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false);\n}\n_s(ElectricalFields, \"e/S201Zj413zFHFxFcP6HoD6jIM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _use_engine_fields__WEBPACK_IMPORTED_MODULE_2__.useEngineFields\n    ];\n});\n_c = ElectricalFields;\nvar _c;\n$RefreshReg$(_c, \"ElectricalFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/daily-checks/engine-checks/pre-startup-checks/electrical-fields.tsx\n"));

/***/ })

});