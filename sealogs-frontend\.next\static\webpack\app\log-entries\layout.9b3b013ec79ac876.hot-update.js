"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/layout",{

/***/ "(app-pages-browser)/./src/app/lib/actions.tsx":
/*!*********************************!*\
  !*** ./src/app/lib/actions.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetCrewListWithTrainingStatus: function() { return /* binding */ GetCrewListWithTrainingStatus; },\n/* harmony export */   GetLogBookEntriesMembers: function() { return /* binding */ GetLogBookEntriesMembers; },\n/* harmony export */   GetTrainingSessionStatus: function() { return /* binding */ GetTrainingSessionStatus; },\n/* harmony export */   GetVesselListWithTrainingAndMaintenanceStatus: function() { return /* binding */ GetVesselListWithTrainingAndMaintenanceStatus; },\n/* harmony export */   convertTimeFormat: function() { return /* binding */ convertTimeFormat; },\n/* harmony export */   getClientByID: function() { return /* binding */ getClientByID; },\n/* harmony export */   getComponentMaintenanceCheckByMemberId: function() { return /* binding */ getComponentMaintenanceCheckByMemberId; },\n/* harmony export */   getComponentMaintenanceCheckByVesselId: function() { return /* binding */ getComponentMaintenanceCheckByVesselId; },\n/* harmony export */   getCrewByID: function() { return /* binding */ getCrewByID; },\n/* harmony export */   getCrewDuties: function() { return /* binding */ getCrewDuties; },\n/* harmony export */   getCrewDutyByID: function() { return /* binding */ getCrewDutyByID; },\n/* harmony export */   getCrewMembersLogBookEntrySections: function() { return /* binding */ getCrewMembersLogBookEntrySections; },\n/* harmony export */   getDashboardVesselList: function() { return /* binding */ getDashboardVesselList; },\n/* harmony export */   getDepartmentList: function() { return /* binding */ getDepartmentList; },\n/* harmony export */   getFieldName: function() { return /* binding */ getFieldName; },\n/* harmony export */   getInventoryByID: function() { return /* binding */ getInventoryByID; },\n/* harmony export */   getInventoryByVesselId: function() { return /* binding */ getInventoryByVesselId; },\n/* harmony export */   getInventoryCategory: function() { return /* binding */ getInventoryCategory; },\n/* harmony export */   getInventoryCategoryByID: function() { return /* binding */ getInventoryCategoryByID; },\n/* harmony export */   getInventoryList: function() { return /* binding */ getInventoryList; },\n/* harmony export */   getLogBookByID: function() { return /* binding */ getLogBookByID; },\n/* harmony export */   getLogBookEntries: function() { return /* binding */ getLogBookEntries; },\n/* harmony export */   getLogBookEntryByID: function() { return /* binding */ getLogBookEntryByID; },\n/* harmony export */   getMaintenanceCategory: function() { return /* binding */ getMaintenanceCategory; },\n/* harmony export */   getMaintenanceCategoryByID: function() { return /* binding */ getMaintenanceCategoryByID; },\n/* harmony export */   getMaintenanceCheckByID: function() { return /* binding */ getMaintenanceCheckByID; },\n/* harmony export */   getMaintenanceCheckSubTaskByID: function() { return /* binding */ getMaintenanceCheckSubTaskByID; },\n/* harmony export */   getOneClient: function() { return /* binding */ getOneClient; },\n/* harmony export */   getSeaLogsGroups: function() { return /* binding */ getSeaLogsGroups; },\n/* harmony export */   getSeaLogsMemberComments: function() { return /* binding */ getSeaLogsMemberComments; },\n/* harmony export */   getSeaLogsMembers: function() { return /* binding */ getSeaLogsMembers; },\n/* harmony export */   getSeaLogsMembersList: function() { return /* binding */ getSeaLogsMembersList; },\n/* harmony export */   getSeaTimeReport: function() { return /* binding */ getSeaTimeReport; },\n/* harmony export */   getSectionMemberComments: function() { return /* binding */ getSectionMemberComments; },\n/* harmony export */   getSignatureUrl: function() { return /* binding */ getSignatureUrl; },\n/* harmony export */   getSupplier: function() { return /* binding */ getSupplier; },\n/* harmony export */   getSupplierByID: function() { return /* binding */ getSupplierByID; },\n/* harmony export */   getTrainingLocations: function() { return /* binding */ getTrainingLocations; },\n/* harmony export */   getTrainingSessionByID: function() { return /* binding */ getTrainingSessionByID; },\n/* harmony export */   getTrainingSessionDues: function() { return /* binding */ getTrainingSessionDues; },\n/* harmony export */   getTrainingSessionDuesByMemberId: function() { return /* binding */ getTrainingSessionDuesByMemberId; },\n/* harmony export */   getTrainingSessionDuesByVesselId: function() { return /* binding */ getTrainingSessionDuesByVesselId; },\n/* harmony export */   getTrainingSessionsByVesselId: function() { return /* binding */ getTrainingSessionsByVesselId; },\n/* harmony export */   getTrainingTypeByID: function() { return /* binding */ getTrainingTypeByID; },\n/* harmony export */   getTrainingTypes: function() { return /* binding */ getTrainingTypes; },\n/* harmony export */   getTrainingTypesList: function() { return /* binding */ getTrainingTypesList; },\n/* harmony export */   getVesselBriefList: function() { return /* binding */ getVesselBriefList; },\n/* harmony export */   getVesselByID: function() { return /* binding */ getVesselByID; },\n/* harmony export */   getVesselList: function() { return /* binding */ getVesselList; },\n/* harmony export */   isOverDueTask: function() { return /* binding */ isOverDueTask; },\n/* harmony export */   upcomingScheduleDate: function() { return /* binding */ upcomingScheduleDate; },\n/* harmony export */   userHasRescueVessel: function() { return /* binding */ userHasRescueVessel; }\n/* harmony export */ });\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _logbook_configuration__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _offline_models_vessel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _offline_models_sectionMemberComment__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../offline/models/sectionMemberComment */ \"(app-pages-browser)/./src/app/offline/models/sectionMemberComment.js\");\n/* harmony import */ var _offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _offline_models_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../offline/models/client */ \"(app-pages-browser)/./src/app/offline/models/client.js\");\n/* harmony import */ var _graphQL_query_GET_CREW_MEMBERS__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./graphQL/query/GET_CREW_MEMBERS */ \"(app-pages-browser)/./src/app/lib/graphQL/query/GET_CREW_MEMBERS.ts\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aws-sdk */ \"(app-pages-browser)/./node_modules/.pnpm/aws-sdk@2.1692.0/node_modules/aws-sdk/lib/browser.js\");\n/* harmony import */ var aws_sdk__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(aws_sdk__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/.pnpm/buffer@4.9.2/node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ getVesselByID,getVesselList,getVesselBriefList,getDashboardVesselList,getInventoryList,getComponentMaintenanceCheckByVesselId,getTrainingSessionsByVesselId,getTrainingSessionDuesByVesselId,getTrainingSessionDues,getTrainingSessionDuesByMemberId,getInventoryByVesselId,getCrewDuties,getSupplier,getLogBookEntries,getSeaLogsMemberComments,getSeaLogsMembersList,getSeaLogsMembers,getCrewByID,getSeaLogsGroups,getTrainingSessionByID,getTrainingTypeByID,getTrainingTypes,getTrainingLocations,getInventoryCategory,getMaintenanceCategory,GetLogBookEntriesMembers,getInventoryByID,getSupplierByID,getMaintenanceCheckByID,getCrewDutyByID,getInventoryCategoryByID,getMaintenanceCategoryByID,getComponentMaintenanceCheckByMemberId,getClientByID,getMaintenanceCheckSubTaskByID,upcomingScheduleDate,getTrainingTypesList,isOverDueTask,GetTrainingSessionStatus,getLogBookByID,getLogBookEntryByID,getOneClient,getCrewMembersLogBookEntrySections,GetCrewListWithTrainingStatus,GetVesselListWithTrainingAndMaintenanceStatus,getSectionMemberComments,getFieldName,getDepartmentList,getSeaTimeReport,convertTimeFormat,userHasRescueVessel,getSignatureUrl auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$(), _s7 = $RefreshSig$(), _s8 = $RefreshSig$(), _s9 = $RefreshSig$(), _s10 = $RefreshSig$(), _s11 = $RefreshSig$(), _s12 = $RefreshSig$(), _s13 = $RefreshSig$(), _s14 = $RefreshSig$(), _s15 = $RefreshSig$(), _s16 = $RefreshSig$(), _s17 = $RefreshSig$(), _s18 = $RefreshSig$(), _s19 = $RefreshSig$(), _s20 = $RefreshSig$(), _s21 = $RefreshSig$(), _s22 = $RefreshSig$(), _s23 = $RefreshSig$(), _s24 = $RefreshSig$(), _s25 = $RefreshSig$(), _s26 = $RefreshSig$(), _s27 = $RefreshSig$(), _s28 = $RefreshSig$(), _s29 = $RefreshSig$(), _s30 = $RefreshSig$(), _s31 = $RefreshSig$(), _s32 = $RefreshSig$(), _s33 = $RefreshSig$(), _s34 = $RefreshSig$(), _s35 = $RefreshSig$(), _s36 = $RefreshSig$(), _s37 = $RefreshSig$(), _s38 = $RefreshSig$(), _s39 = $RefreshSig$(), _s40 = $RefreshSig$(), _s41 = $RefreshSig$(), _s42 = $RefreshSig$(), _s43 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ACCOUNT_ID = \"ddde1c1cd1aa25641691808dcbafdeb7\";\nconst ACCESS_KEY_ID = \"06c3e13a539f24e6fdf7075bf381bf5e\";\nconst SECRET_ACCESS_KEY = \"0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8\";\nconst s3Client = new (aws_sdk__WEBPACK_IMPORTED_MODULE_12___default().S3)({\n    endpoint: \"https://\".concat(ACCOUNT_ID, \".r2.cloudflarestorage.com\"),\n    accessKeyId: ACCESS_KEY_ID,\n    secretAccessKey: SECRET_ACCESS_KEY,\n    signatureVersion: \"v4\",\n    region: \"auto\"\n});\nasync function getVesselByID(vesselId, setVessel) {\n    let offline = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    _s();\n    const vesselModel = new _offline_models_vessel__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryVesselInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.VESSEL_INFO, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneVessel;\n            if (data) {\n                setVessel(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselInfo error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselInfo();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadVesselInfo = async ()=>{\n        if (offline) {\n            const data = await vesselModel.getById(vesselId);\n            setVessel(data);\n        } else {\n            await queryVesselInfo({\n                variables: {\n                    id: +vesselId\n                }\n            });\n        }\n    };\n}\n_s(getVesselByID, \"h7K6B3bx4OCc6oZiyjdGBv6gR5g=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getVesselList(handleSetVessels) {\n    let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    _s1();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const vesselModel = new _offline_models_vessel__WEBPACK_IMPORTED_MODULE_5__[\"default\"]();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.VESSEL_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        if (offline) {\n            const response = await vesselModel.getAll();\n            handleSetVessels(response);\n        } else {\n            await queryVessels({\n                variables: {\n                    limit: 200,\n                    offset: 0\n                }\n            });\n        }\n    };\n}\n_s1(getVesselList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getVesselBriefList(handleSetVessels) {\n    _s2();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                handleSetVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0,\n                filter: {\n                    archived: {\n                        eq: false\n                    }\n                }\n            }\n        });\n    };\n}\n_s2(getVesselBriefList, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getDashboardVesselList(handleSetVessels) {\n    let archived = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n    _s3();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.DASHBOARD_VESSEL_LIST, {\n        fetchPolicy: \"no-cache\",\n        onCompleted: async (queryVesselResponse)=>{\n            if (queryVesselResponse.readDashboardData && \"object\" !== \"undefined\") {\n                var _response_data_readVessels, _response_data;\n                const vessels = queryVesselResponse.readDashboardData[0].vessels;\n                const vesselIDs = vessels.map((item)=>item.id);\n                let response = [];\n                if (vesselIDs.length > 0) {\n                    response = await getVessselsWithLatestStatus({\n                        variables: {\n                            vesselFilter: {\n                                id: {\n                                    in: vesselIDs\n                                }\n                            },\n                            filter: {\n                                archived: {\n                                    eq: false\n                                }\n                            }\n                        }\n                    });\n                }\n                var _response_data_readVessels_nodes;\n                const vesselsWithLatestStatus = (_response_data_readVessels_nodes = (_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_readVessels = _response_data.readVessels) === null || _response_data_readVessels === void 0 ? void 0 : _response_data_readVessels.nodes) !== null && _response_data_readVessels_nodes !== void 0 ? _response_data_readVessels_nodes : [];\n                // Loop through vessels and save the tasksDue and trainingsDue properties to localStorage with this format for the keys: 'tasksDue-id' and 'trainingsDue-id'\n                for(let i = 0; i < vessels.length; i++){\n                    const vessel = vessels[i];\n                    localStorage.setItem(\"tasksDue-\".concat(vessel.id), vessel.tasksDue);\n                    localStorage.setItem(\"trainingsDue-\".concat(vessel.id), vessel.trainingsDue);\n                }\n                const vesselsWithStatus = vessels.map(function(vessel) {\n                    const vesselWithStatus = vesselsWithLatestStatus.find((item)=>item.id == vessel.id);\n                    var _vesselWithStatus_statusHistory_nodes;\n                    const statusHistory = (_vesselWithStatus_statusHistory_nodes = vesselWithStatus === null || vesselWithStatus === void 0 ? void 0 : vesselWithStatus.statusHistory.nodes) !== null && _vesselWithStatus_statusHistory_nodes !== void 0 ? _vesselWithStatus_statusHistory_nodes : [];\n                    const status = statusHistory.length > 0 ? statusHistory[0] : null;\n                    return {\n                        ...vessel,\n                        status: status\n                    };\n                });\n                handleSetVessels(vesselsWithStatus);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const [getVessselsWithLatestStatus] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_13__.GET_VESSELS_WITH_LATEST_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                limit: 200,\n                offset: 0,\n                archived: archived\n            }\n        });\n    };\n}\n_s3(getDashboardVesselList, \"w4a71J9ei5vW+1Nz34CbtDjQ0Z8=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getInventoryList(setInventories) {\n    _s4();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadInventories();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [queryInventories] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_INVENTORIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readInventories.nodes;\n            if (data) {\n                setInventories(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoriesEntry error\", error);\n        }\n    });\n    const loadInventories = async ()=>{\n        await queryInventories();\n    };\n}\n_s4(getInventoryList, \"WzXjiklTKNZA+IjmPPWkAGUZgLE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getComponentMaintenanceCheckByVesselId(vesselId, handleSetMaintenanceTasks) {\n    _s5();\n    const [querysetMaintenanceCheckInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CHECK_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceCheckList[0].list;\n            if (data) {\n                handleSetMaintenanceTasks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querysetMaintenanceCheckInfo error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMaintenanceCheckInfo();\n    }, []);\n    const loadMaintenanceCheckInfo = async ()=>{\n        await querysetMaintenanceCheckInfo({\n            variables: {\n                inventoryID: 0,\n                vesselID: +vesselId,\n                archived: 0\n            }\n        });\n    };\n}\n_s5(getComponentMaintenanceCheckByVesselId, \"7bMp7UWStPZ7d7XyHS6q7Tm7M9s=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingSessionsByVesselId(vesselId, setTrainingSessions) {\n    _s6();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingSessions] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.TRAINING_SESSION_BY_VESSEL, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            if (data) {\n                setTrainingSessions(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSessions error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessions();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingSessions = async ()=>{\n        await queryTrainingSessions({\n            variables: {\n                vesselID: +vesselId,\n                limit: 10\n            }\n        });\n    };\n}\n_s6(getTrainingSessionsByVesselId, \"h2XOP8EbMLNs22LhHJF8iqy/fbI=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingSessionDuesByVesselId(vesselId, setTrainingSessionDues) {\n    _s7();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [readTrainingSessionDues, { loading: readTrainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                // const filteredData = data.filter((item: any) =>\n                //     item.vessel.seaLogsMembers.nodes.some((m: any) => {\n                //         return m.id === item.memberID\n                //     }),\n                // )\n                // const dueWithStatus = filteredData.map((due: any) => {\n                const dueWithStatus = data.map((due)=>{\n                    return {\n                        ...due,\n                        status: GetTrainingSessionStatus(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                // const groupedDues = dueWithStatus.reduce(\n                //     (acc: any, due: any) => {\n                //         const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`\n                //         if (!acc[key]) {\n                //             acc[key] = {\n                //                 id: due.id,\n                //                 vesselID: due.vesselID,\n                //                 vessel: due.vessel,\n                //                 trainingTypeID: due.trainingTypeID,\n                //                 trainingType: due.trainingType,\n                //                 dueDate: due.dueDate,\n                //                 status: due.status,\n                //                 members: [],\n                //             }\n                //         }\n                //         acc[key].members.push(due.member)\n                //         return acc\n                //     },\n                //     {},\n                // )\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (!existingMember) {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            // setTrainingSessionDues(dueWithStatus)\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessionDues();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingSessionDues = async ()=>{\n        const dueFilter = {};\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        dueFilter.dueDate = {\n            ne: null\n        };\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n}\n_s7(getTrainingSessionDuesByVesselId, \"DBHw3S/SDZjcE2hbvpHKtugJ2XU=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingSessionDues(setTrainingSessionDues) {\n    _s8();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [readTrainingSessionDues, { loading: readTrainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                // const filteredData = data.filter((item: any) =>\n                //     item.vessel.seaLogsMembers.nodes.some((m: any) => {\n                //         return m.id === item.memberID\n                //     }),\n                // )\n                // const dueWithStatus = filteredData.map((due: any) => {\n                const dueWithStatus = data.map((due)=>{\n                    return {\n                        ...due,\n                        status: GetTrainingSessionStatus(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                // const groupedDues = dueWithStatus.reduce(\n                //     (acc: any, due: any) => {\n                //         const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`\n                //         if (!acc[key]) {\n                //             acc[key] = {\n                //                 id: due.id,\n                //                 vesselID: due.vesselID,\n                //                 vessel: due.vessel,\n                //                 trainingTypeID: due.trainingTypeID,\n                //                 trainingType: due.trainingType,\n                //                 dueDate: due.dueDate,\n                //                 status: due.status,\n                //                 members: [],\n                //             }\n                //         }\n                //         acc[key].members.push(due.member)\n                //         return acc\n                //     },\n                //     {},\n                // )\n                const groupedDues = dueWithStatus.map((due)=>{\n                    return {\n                        ...due,\n                        members: []\n                    };\n                });\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, [\n                        group.member\n                    ]);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            // setTrainingSessionDues(dueWithStatus)\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessionDues();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingSessionDues = async ()=>{\n        const dueFilter = {};\n        dueFilter.dueDate = {\n            ne: null\n        };\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n}\n_s8(getTrainingSessionDues, \"DBHw3S/SDZjcE2hbvpHKtugJ2XU=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingSessionDuesByMemberId(memberId, setTrainingSessionDues) {\n    _s9();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [readTrainingSessionDues, { loading: readTrainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                const filteredData = data.filter((item)=>item.memberID === memberId);\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: GetTrainingSessionStatus(due)\n                    };\n                });\n                const filteredDueWithStatus = dueWithStatus.filter((item)=>{\n                    return item.status.isOverdue || item.status.isOverdue === false && item.status.dueWithinSevenDays === true;\n                });\n                const groupedDues = filteredDueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.memberID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            memberID: due.memberID,\n                            member: due.member,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status\n                        };\n                    }\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>({\n                        id: group.id,\n                        memberID: group.memberID,\n                        member: group.member,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate\n                    }));\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    const loadTrainingSessionDues = async ()=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        //dueFilter.dueDate = { ne: null }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessionDues();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n}\n_s9(getTrainingSessionDuesByMemberId, \"DBHw3S/SDZjcE2hbvpHKtugJ2XU=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getInventoryByVesselId(vesselId, setInventories) {\n    _s10();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryInventoriesByVessel] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_INVENTORY_BY_VESSEL_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readInventories.nodes;\n            if (data) {\n                setInventories(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventories error\", error);\n        }\n    });\n    const loadInventories = async ()=>{\n        await queryInventoriesByVessel({\n            variables: {\n                vesselId: +vesselId\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadInventories();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n}\n_s10(getInventoryByVesselId, \"lNw29q67sNMnqkn2fwOwXUmIPoY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getCrewDuties(setCrewDuty) {\n    _s11();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryCrewDuty] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.CREW_DUTY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewDuties.nodes;\n            if (data) {\n                setCrewDuty(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewDuty error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadCrewDuty();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadCrewDuty = async ()=>{\n        await queryCrewDuty();\n    };\n}\n_s11(getCrewDuties, \"Wl0GD3ULqzwlRU77VWe/E2xEoY4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSupplier(setSupplier) {\n    _s12();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySupplier] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_SUPPLIER, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSuppliers.nodes;\n            if (data) {\n                setSupplier(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySupplier error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadSupplier();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadSupplier = async ()=>{\n        await querySupplier();\n    };\n}\n_s12(getSupplier, \"iE5HYSCIGWEtO9CnUH96DVVpsK8=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getLogBookEntries(vesselId, setLogBookEntries) {\n    let offline = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    _s13();\n    const logBookEntryModel = new _offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_7__[\"default\"]();\n    const crewMembers_LogBookEntrySectionModel = new _offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const seaLogsMemberModel = new _offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    /*const [queryLogBookEntries] = useLazyQuery(GET_LOGBOOKENTRY, {\r\n        fetchPolicy: 'cache-and-network',\r\n        onCompleted: (response: any) => {\r\n            const crew = response.readCrewMembers_LogBookEntrySections.nodes\r\n            const entries = response.GetLogBookEntries.nodes\r\n            const data = entries.map((entry: any) => {\r\n                const crewData = crew.filter(\r\n                    (crewMember: any) => crewMember.logBookEntryID === entry.id,\r\n                )\r\n                return {\r\n                    ...entry,\r\n                    crew: crewData,\r\n                }\r\n            })\r\n            if (data) {\r\n                setLogBookEntries(data)\r\n            }\r\n        },\r\n        onError: (error: any) => {\r\n            console.error('queryLogBookEntries error', error)\r\n        },\r\n    })*/ const logBookEntriesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [fetchCrewMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_graphQL_query_GET_CREW_MEMBERS__WEBPACK_IMPORTED_MODULE_11__.GET_CREW_MEMBERS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (crewResponse)=>{\n            var _crewResponse_readCrewMembers_LogBookEntrySections;\n            var _crewResponse_readCrewMembers_LogBookEntrySections_nodes;\n            const crew = (_crewResponse_readCrewMembers_LogBookEntrySections_nodes = crewResponse === null || crewResponse === void 0 ? void 0 : (_crewResponse_readCrewMembers_LogBookEntrySections = crewResponse.readCrewMembers_LogBookEntrySections) === null || _crewResponse_readCrewMembers_LogBookEntrySections === void 0 ? void 0 : _crewResponse_readCrewMembers_LogBookEntrySections.nodes) !== null && _crewResponse_readCrewMembers_LogBookEntrySections_nodes !== void 0 ? _crewResponse_readCrewMembers_LogBookEntrySections_nodes : [];\n            const entries = logBookEntriesRef.current;\n            const data = entries.map((entry)=>{\n                const crewData = crew.filter((crewMember)=>crewMember.logBookEntryID === entry.id);\n                return {\n                    ...entry,\n                    crew: crewData\n                };\n            });\n            setLogBookEntries(data);\n        },\n        onError: (error)=>{\n            console.error(\"fetchCrewMembers error\", error);\n        }\n    });\n    const [queryLogBookEntries] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_LOGBOOKENTRY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _response_GetLogBookEntries;\n            var _response_GetLogBookEntries_nodes;\n            const entries = (_response_GetLogBookEntries_nodes = response === null || response === void 0 ? void 0 : (_response_GetLogBookEntries = response.GetLogBookEntries) === null || _response_GetLogBookEntries === void 0 ? void 0 : _response_GetLogBookEntries.nodes) !== null && _response_GetLogBookEntries_nodes !== void 0 ? _response_GetLogBookEntries_nodes : [];\n            logBookEntriesRef.current = entries;\n            const logBookEntryIDs = entries.map((entry)=>entry.id);\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(logBookEntryIDs)) {\n                fetchCrewMembers({\n                    variables: {\n                        logBookEntryIDs\n                    }\n                });\n            } else {\n                setLogBookEntries([]);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntries error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadLogBookEntries();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadLogBookEntries = async ()=>{\n        if (offline) {\n            const crew = await crewMembers_LogBookEntrySectionModel.getAll();\n            const entries = await logBookEntryModel.getByVesselId(vesselId);\n            const data = await Promise.all(entries.map(async (entry)=>{\n                const crewList = crew.filter((crewMember)=>crewMember.logBookEntryID === entry.id);\n                const crewData = await Promise.all(crewList.map(async (member)=>{\n                    const crewMember = await seaLogsMemberModel.getById(member.crewMemberID);\n                    return {\n                        ...member,\n                        crewMember: crewMember\n                    };\n                }));\n                return {\n                    ...entry,\n                    crew: crewData\n                };\n            }));\n            if (data) {\n                setLogBookEntries(data);\n            }\n        } else {\n            await queryLogBookEntries({\n                variables: {\n                    vesselId: +vesselId\n                }\n            });\n        }\n    };\n}\n_s13(getLogBookEntries, \"wLW8i7E7KVQLovWd5KrYeYmofNg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSeaLogsMemberComments(setSeaLogsMemberComments) {\n    _s14();\n    const online = true // To be replaced with useOnline()\n    ;\n    const sectionMemberCommentsModel = new _offline_models_sectionMemberComment__WEBPACK_IMPORTED_MODULE_6__[\"default\"]();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySeaLogsMemberComments] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_SEALOGS_MEMBER_COMMENTS, {\n        // fetchPolicy: 'cache-and-network',\n        onCompleted: (response)=>{\n            const data = response.readSectionMemberComments.nodes;\n            if (data) {\n                setSeaLogsMemberComments(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadNotification();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadNotification = async ()=>{\n        await querySeaLogsMemberComments({\n            variables: {\n                start: 1,\n                limit: 0\n            }\n        });\n    };\n}\n_s14(getSeaLogsMemberComments, \"w6j0oNF/dncXH1I8jVhDS48ISZY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSeaLogsMembersList(setCrewMembers) {\n    let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    _s15();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const slmModel = new _offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.CREW_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewMembers(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadCrewMembers = async ()=>{\n        if (offline) {\n            const crew = await slmModel.getAll();\n            if (crew) {\n                setCrewMembers(crew);\n            }\n        } else {\n            await querySeaLogsMembersList({\n                variables: {\n                    limit: 50,\n                    filter: {\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n}\n_s15(getSeaLogsMembersList, \"p3lMgLQt34BZIQ01LBEaBL9nEIY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSeaLogsMembers(crewMemberIDs, setCrewMembers) {\n    _s16();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySeaLogsMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewMembers(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembers error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadCrewMembers = async ()=>{\n        await querySeaLogsMembers({\n            variables: {\n                crewMemberIDs: crewMemberIDs\n            }\n        });\n    };\n}\n_s16(getSeaLogsMembers, \"C9XgxHawxGcRSZJA+pJqS8FTYs4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getCrewByID(crewMemberID, setCrewMembers) {\n    _s17();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySeaLogsMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_CREW_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneSeaLogsMember;\n            if (data) {\n                setCrewMembers(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembers error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadCrewMembers = async ()=>{\n        await querySeaLogsMembers({\n            variables: {\n                crewMemberID: crewMemberID\n            }\n        });\n    };\n}\n_s17(getCrewByID, \"C9XgxHawxGcRSZJA+pJqS8FTYs4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n// Note from Esthon: I commented this out because this function would be too complicated to use with paginations,\n// among other issues particularty in the CrewTrainingList component\n/* export async function getTrainingSessions(setTrainingSessions: any) {\r\n    const [isLoading, setIsLoading] = useState(true)\r\n    const [queryTrainingSessions] = useLazyQuery(TRAINING_SESSIONS, {\r\n        fetchPolicy: 'cache-and-network',\r\n        onCompleted: (response: any) => {\r\n            const data = response.readTrainingSessions.nodes\r\n            if (data) {\r\n                setTrainingSessions(data)\r\n            }\r\n        },\r\n        onError: (error: any) => {\r\n            console.error('queryTrainingSessions error', error)\r\n        },\r\n    })\r\n    useEffect(() => {\r\n        if (isLoading) {\r\n            loadTrainingSessions()\r\n            setIsLoading(false)\r\n        }\r\n    }, [isLoading])\r\n    const loadTrainingSessions = async () => {\r\n        await queryTrainingSessions()\r\n    }\r\n} */ async function getSeaLogsGroups(setSeaLogsGroups) {\n    _s18();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySeaLogsGroups] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.SEALOGS_GROUP, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsGroups.nodes;\n            if (data) {\n                setSeaLogsGroups(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsGroups error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadSeaLogsGroups();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadSeaLogsGroups = async ()=>{\n        await querySeaLogsGroups();\n    };\n}\n_s18(getSeaLogsGroups, \"aNNicmExLEkAx9IcZeNXzMQ2ZLc=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingSessionByID(id, setTrainingSession) {\n    _s19();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingSessionByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.TRAINING_SESSION_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingSession;\n            if (data) {\n                setTrainingSession(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingSession error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingSessionByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingSessionByID = async ()=>{\n        await queryTrainingSessionByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s19(getTrainingSessionByID, \"41imxJ9F2VJpSxkzmxtwLpk6Ff0=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingTypeByID(id, setTrainingType) {\n    _s20();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingTypeByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.TRAINING_TYPE_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneTrainingType;\n            if (data) {\n                setTrainingType(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingType error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingTypeByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingTypeByID = async ()=>{\n        await queryTrainingTypeByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s20(getTrainingTypeByID, \"3sTdcjmYHBBRYGKr99Lxi/nMyYU=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingTypes(setTrainingTypes) {\n    _s21();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.CREW_TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingTypes();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingTypes = async ()=>{\n        await queryTrainingTypes();\n    };\n}\n_s21(getTrainingTypes, \"QtWe1wt21WpoM8nD8Lnt7NCntn4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getTrainingLocations(setTrainingLocations) {\n    _s22();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.TRAINING_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingLocations.nodes;\n            if (data) {\n                setTrainingLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingLocations error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingLocations();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingLocations = async ()=>{\n        await queryTrainingLocations();\n    };\n}\n_s22(getTrainingLocations, \"oc0f5gLNL8lvyinMAuRWMNOCG6k=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getInventoryCategory(setInventoryCategory) {\n    _s23();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryInventoryCategory] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_INVENTORY_CATEGORY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readInventoryCategories.nodes;\n            if (data) {\n                setInventoryCategory(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoryCategory error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadInventoryCategory();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadInventoryCategory = async ()=>{\n        await queryInventoryCategory();\n    };\n}\n_s23(getInventoryCategory, \"O717TQl1njEQWUy22XQyGVeMzMQ=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getMaintenanceCategory(setMaintenanceCategory) {\n    _s24();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryMaintenanceCategory] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CATEGORY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readMaintenanceCategories.nodes;\n            if (data) {\n                setMaintenanceCategory(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCategory error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceCategory();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceCategory = async ()=>{\n        var _localStorage_getItem;\n        await queryMaintenanceCategory({\n            variables: {\n                clientID: +((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n            }\n        });\n    };\n}\n_s24(getMaintenanceCategory, \"fjP647C2yVQ/lxqDJOKJ7JdTThs=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function GetLogBookEntriesMembers(setMemberIds) {\n    let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    _s25();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const logbookModel = new _offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_7__[\"default\"]();\n    const [queryMemberIds] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GetLogBookEntriesMemberIds, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readLogBookEntries;\n            if (data) {\n                setMemberIds(data.nodes.filter((entry)=>entry.vehicle.id > 0).flatMap((entry)=>entry.logBookEntrySections.nodes).flatMap((section)=>+section.id));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMemberIds error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMemberIds();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMemberIds = async ()=>{\n        if (offline) {\n            let logbooks = await logbookModel.getAll();\n            // Filter by state is Editing or Reopened and\n            logbooks = logbooks.filter((logbook)=>{\n                return (logbook.state == \"Editing\" || logbook.state == \"Reopened\") && logbook.vehicleID > 0;\n            });\n            logbooks = logbooks.map((logbook)=>{\n                return {\n                    ...logbook,\n                    logBookEntrySections: {\n                        nodes: logbook.logBookEntrySections.nodes.filter((section)=>{\n                            var _section_logBookComponentClass;\n                            return (_section_logBookComponentClass = section.logBookComponentClass) === null || _section_logBookComponentClass === void 0 ? void 0 : _section_logBookComponentClass.includes(\"CrewMembers_LogBookComponent\");\n                        })\n                    }\n                };\n            });\n            if (logbooks) {\n                const crewMembers = logbooks.flatMap((entry)=>entry.logBookEntrySections.nodes).flatMap((section)=>section.id);\n                setMemberIds(crewMembers);\n            }\n        } else {\n            await queryMemberIds();\n        }\n    };\n}\n_s25(GetLogBookEntriesMembers, \"GwCG4JOMYN2W2LgXoKDypdobrT4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c = GetLogBookEntriesMembers;\nasync function getInventoryByID(id, setInventory) {\n    _s26();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryInventoryByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_INVENTORY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneInventory;\n            if (data) {\n                setInventory(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoryByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadInventoryByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadInventoryByID = async ()=>{\n        await queryInventoryByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s26(getInventoryByID, \"9yvEKVnmxFWoWa2vKvBBYDBa55o=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSupplierByID(id, setSupplier) {\n    _s27();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySupplierByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_SUPPLIER_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneSupplier;\n            if (data) {\n                setSupplier(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySupplierByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadSupplierByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadSupplierByID = async ()=>{\n        await querySupplierByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s27(getSupplierByID, \"g6Ur8tVpvcKyPCGb05M5BbwYycA=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getMaintenanceCheckByID(id, setMaintenanceCheck) {\n    _s28();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryMaintenanceCheckByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CHECK_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneComponentMaintenanceCheck;\n            if (data) {\n                setMaintenanceCheck(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCheckByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceCheckByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceCheckByID = async ()=>{\n        await queryMaintenanceCheckByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s28(getMaintenanceCheckByID, \"PBX11Ab4IVFbX0Hkvj6S+ZJfVio=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getCrewDutyByID(id, setCrewDuty) {\n    _s29();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryCrewDutyByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_CREW_DUTY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneCrewDuty;\n            if (data) {\n                setCrewDuty(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewDutyByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadCrewDutyByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadCrewDutyByID = async ()=>{\n        await queryCrewDutyByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s29(getCrewDutyByID, \"nQVUTLAM+1rtPDcudg8T16ssU64=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getInventoryCategoryByID(id, setInventoryCategory) {\n    _s30();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryInventoryCategoryByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_INVENTORY_CATEGORY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneInventoryCategory;\n            if (data) {\n                setInventoryCategory(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventoryCategoryByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadInventoryCategoryByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadInventoryCategoryByID = async ()=>{\n        await queryInventoryCategoryByID({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s30(getInventoryCategoryByID, \"tcP4DbWvr+kASE7hM6J3DZFyh68=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getMaintenanceCategoryByID(id, setMaintenanceCategory) {\n    _s31();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryMaintenanceCategoryByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CATEGORY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneMaintenanceCategory;\n            if (data) {\n                setMaintenanceCategory(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCategoryByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceCategoryByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceCategoryByID = async ()=>{\n        var _localStorage_getItem;\n        await queryMaintenanceCategoryByID({\n            variables: {\n                id: +id,\n                clientID: +((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0)\n            }\n        });\n    };\n}\n_s31(getMaintenanceCategoryByID, \"oTtj3LD/myps+2KdBXGyx+JCBGs=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getComponentMaintenanceCheckByMemberId(memberId, handleSetMaintenanceTasks) {\n    _s32();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTaskList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CHECK_BY_MEMBER_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readComponentMaintenanceChecks.nodes;\n            if (data) {\n                handleSetMaintenanceTasks(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTaskList error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTaskList();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTaskList = async ()=>{\n        await queryTaskList({\n            variables: {\n                memberId: +memberId\n            }\n        });\n    };\n}\n_s32(getComponentMaintenanceCheckByMemberId, \"MGCbMfH9TlUlOHN7P6StrWi82rc=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getClientByID(id, setClient) {\n    _s33();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryClientByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_CLIENT_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readClients;\n            if (data) {\n                setClient(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryClientByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadClientByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadClientByID = async ()=>{\n        await queryClientByID({\n            variables: {\n                clientIDs: [\n                    id\n                ]\n            }\n        });\n    };\n}\n_s33(getClientByID, \"Ku2fHZTGejxnpy/9DG6CpYWu9xY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getMaintenanceCheckSubTaskByID(id, setMaintenanceCheckSubTask) {\n    _s34();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryMaintenanceCheckSubTask] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_MAINTENANCE_CHECK_SUBTASK, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readMaintenanceCheckSubTasks.nodes;\n            if (data) {\n                setMaintenanceCheckSubTask(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryMaintenanceCheckSubTask error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadMaintenanceCheckSubTask();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadMaintenanceCheckSubTask = async ()=>{\n        await queryMaintenanceCheckSubTask({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s34(getMaintenanceCheckSubTaskByID, \"HiYdbPIerU3e9InR+A5KxYDBF3Y=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst upcomingScheduleDate = function(maintenanceChecks) {\n    let getRaw = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    const recurringTasks = (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.maintenanceSchedule.__typename) === \"ComponentMaintenanceSchedule\" ? maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.maintenanceSchedule : false;\n    if (recurringTasks) {\n        const occursEveryType = recurringTasks.occursEveryType ? recurringTasks.occursEveryType : \"Days\";\n        if (occursEveryType === \"Hours\" || occursEveryType === \"Uses\") {\n            if (occursEveryType === \"Uses\") {\n                return maintenanceChecks.equipmentUsagesAtCheck + \" Equipment Uses\";\n            }\n            return new Date(maintenanceChecks.dutyHoursAtCheck).toLocaleDateString();\n        } else {\n            const occursEvery = recurringTasks.occursEvery ? recurringTasks.occursEvery : 1;\n            const lastCompletedDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()((maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.dateCompleted) ? new Date(maintenanceChecks.dateCompleted) : new Date()).startOf(\"day\");\n            const nextOccurrence = lastCompletedDate.add(occursEvery, occursEveryType);\n            if (getRaw) {\n                return nextOccurrence.format(\"YYYY-MM-DD\");\n            }\n            return nextOccurrence.format(\"DD/MM/YYYY\");\n        }\n    }\n    return maintenanceChecks.expires ? getRaw ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(maintenanceChecks.expires).format(\"YYYY-MM-DD\") : new Date(maintenanceChecks.expires).toLocaleDateString() : \"NA\";\n};\nasync function getTrainingTypesList(setTrainingTypes) {\n    _s35();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryTrainingTypes] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.TRAINING_TYPES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingTypes.nodes;\n            if (data) {\n                setTrainingTypes(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingTypes error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadTrainingTypes();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadTrainingTypes = async ()=>{\n        await queryTrainingTypes();\n    };\n}\n_s35(getTrainingTypesList, \"QtWe1wt21WpoM8nD8Lnt7NCntn4=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst isOverDueTask = (maintenanceChecks)=>{\n    var _maintenanceChecks_maintenanceSchedule, _maintenanceChecks_maintenanceSchedule1, _maintenanceChecks_maintenanceSchedule2;\n    if ((maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.taskType) === \"EngineHours\") {\n        var _maintenanceChecks_engine;\n        const startHours = maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.startHours;\n        const currentHours = maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : (_maintenanceChecks_engine = maintenanceChecks.engine) === null || _maintenanceChecks_engine === void 0 ? void 0 : _maintenanceChecks_engine.currentHours;\n        var dueHours = startHours - currentHours;\n        if (dueHours > 0) {\n            return {\n                status: \"High\",\n                days: \"Overdue by \" + dueHours + \" engine hours\",\n                day: dueHours\n            };\n        } else {\n            dueHours = dueHours * -1;\n            return {\n                status: \"High\",\n                days: \"Overdue by \" + dueHours + \" engine hours\",\n                day: dueHours\n            };\n        }\n    }\n    if (!(maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.expires) && !(maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.completed) && !(maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : (_maintenanceChecks_maintenanceSchedule = maintenanceChecks.maintenanceSchedule) === null || _maintenanceChecks_maintenanceSchedule === void 0 ? void 0 : _maintenanceChecks_maintenanceSchedule.occursEveryType)) {\n        return {\n            status: \"Medium\",\n            days: \"Open\",\n            ignore: true\n        };\n    }\n    if (!(maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.expires) && (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : (_maintenanceChecks_maintenanceSchedule1 = maintenanceChecks.maintenanceSchedule) === null || _maintenanceChecks_maintenanceSchedule1 === void 0 ? void 0 : _maintenanceChecks_maintenanceSchedule1.occursEveryType) !== \"Hours\") {\n        return {\n            status: \"Completed\",\n            days: \"\"\n        };\n    }\n    if (maintenanceChecks.status === \"Completed\") {\n        return {\n            status: \"Completed\",\n            days: \"Completed on \" + new Date((maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.dateCompleted) ? maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.dateCompleted : maintenanceChecks.completed).toLocaleDateString()\n        };\n    }\n    if (maintenanceChecks.status === \"Save_As_Draft\") {\n        return {\n            status: \"Completed\",\n            days: maintenanceChecks.status.replaceAll(\"_\", \" \")\n        };\n    }\n    const recurringTasks = (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.maintenanceSchedule.__typename) === \"ComponentMaintenanceSchedule\" ? maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.maintenanceSchedule : false;\n    const today = new Date();\n    const daysDifference = (maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : maintenanceChecks.expires) ? Math.ceil((new Date(maintenanceChecks.expires).getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : 0;\n    if ((maintenanceChecks === null || maintenanceChecks === void 0 ? void 0 : (_maintenanceChecks_maintenanceSchedule2 = maintenanceChecks.maintenanceSchedule) === null || _maintenanceChecks_maintenanceSchedule2 === void 0 ? void 0 : _maintenanceChecks_maintenanceSchedule2.occursEveryType) === \"Hours\") {\n        const engineHours = Math.min(...maintenanceChecks.maintenanceSchedule.engineUsage.nodes.filter((engineUsage)=>engineUsage.isScheduled === true || engineUsage.lastScheduleHours > 0).map((engineUsage)=>{\n            return engineUsage.lastScheduleHours + maintenanceChecks.maintenanceSchedule.occursEvery - engineUsage.engine.currentHours;\n        }));\n        const highWarnWithin = 3;\n        const mediumWarnWithin = 5;\n        const lowWarnWithin = 7;\n        if (engineHours < highWarnWithin) {\n            return {\n                status: \"High\",\n                days: engineHours < 0 ? \"Overdue by \" + engineHours * -1 + \" engine hours\" : \"Due - \" + engineHours + \" engine hours\",\n                day: engineHours\n            };\n        }\n        if (engineHours < mediumWarnWithin) {\n            return {\n                status: \"Medium\",\n                days: engineHours < 0 ? \"Overdue by \" + engineHours * -1 + \" engine hours\" : \"Due - \" + engineHours + \" engine hours\",\n                day: engineHours\n            };\n        }\n        if (engineHours < lowWarnWithin) {\n            return {\n                status: \"Low\",\n                days: engineHours < 0 ? \"Overdue by \" + engineHours * -1 + \" engine hours\" : \"Due - \" + engineHours + \" engine hours\",\n                day: engineHours\n            };\n        }\n        return {\n            status: \"Upcoming\",\n            days: \"Due - \" + engineHours + \" engine hours\",\n            day: engineHours\n        };\n    }\n    if (recurringTasks) {\n        const occursEveryType = recurringTasks.occursEveryType ? recurringTasks.occursEveryType : \"Days\";\n        const highWarnWithin = occursEveryType === \"Months\" ? 3 : 1;\n        const mediumWarnWithin = occursEveryType === \"Months\" ? 7 : 3;\n        const lowWarnWithin = occursEveryType === \"Months\" ? 14 : 7;\n        const days = occursEveryType === \"Days\" ? recurringTasks.occursEvery : occursEveryType === \"Months\" ? recurringTasks.occursEvery * 30 : occursEveryType === \"Weeks\" ? recurringTasks.occursEvery * 7 : 0;\n        const nextOccurrence = new Date(new Date(maintenanceChecks.startDate).setDate(new Date(maintenanceChecks.startDate).getDate() + days));\n        const diff = Math.ceil((nextOccurrence.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (occursEveryType === \"Days\" || occursEveryType === \"Months\" || occursEveryType === \"Weeks\") {\n            if (nextOccurrence < new Date(new Date().setDate(today.getDate() + highWarnWithin))) {\n                return {\n                    status: \"High\",\n                    days: diff < 0 ? diff * -1 + \" days ago\" : \"Due - \" + diff + \" days\",\n                    day: diff\n                };\n            }\n            if (nextOccurrence < new Date(new Date().setDate(today.getDate() + mediumWarnWithin))) {\n                return {\n                    status: \"Medium\",\n                    days: diff < 0 ? diff * -1 + \" days ago\" : \"Due - \" + diff + \" days\",\n                    day: diff\n                };\n            }\n            if (nextOccurrence < new Date(new Date().setDate(today.getDate() + lowWarnWithin))) {\n                return {\n                    status: \"Low\",\n                    days: diff < 0 ? diff * -1 + \" days ago\" : \"Due - \" + diff + \" days\",\n                    day: diff\n                };\n            }\n            return {\n                status: \"Upcoming\",\n                days: \"Due - \" + diff + \" days\",\n                day: diff\n            };\n        } else {\n            if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + highWarnWithin))) {\n                return {\n                    status: \"High\",\n                    days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n                };\n            }\n            if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + mediumWarnWithin))) {\n                return {\n                    status: \"Medium\",\n                    days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n                };\n            }\n            if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + lowWarnWithin))) {\n                return {\n                    status: \"Low\",\n                    days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n                };\n            }\n        }\n    } else {\n        const highWarnWithin = 1;\n        const mediumWarnWithin = 3;\n        const lowWarnWithin = 7;\n        if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + highWarnWithin))) {\n            return {\n                status: \"High\",\n                days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n            };\n        }\n        if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + mediumWarnWithin))) {\n            return {\n                status: \"Medium\",\n                days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n            };\n        }\n        if (new Date(maintenanceChecks.expires) < new Date(new Date().setDate(today.getDate() + lowWarnWithin))) {\n            return {\n                status: \"Low\",\n                days: daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\"\n            };\n        }\n    }\n    return {\n        status: \"Upcoming\",\n        days: \"Due - \" + daysDifference + \" days\"\n    };\n};\nconst GetTrainingSessionStatus = (due)=>{\n    const classes = {\n        low: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n        medium: \"text-fire-bush-600 bg-fire-bush-100 px-2 py-0.5 border rounded  border-fire-bush-600\",\n        high: \"alert\"\n    };\n    const dueDate = due.dueDate;\n    const today = new Date();\n    const oneDayMilisecond = 1000 * 60 * 60 * 24;\n    const daysDifference = Math.ceil((new Date(dueDate).getTime() - today.getTime()) / oneDayMilisecond);\n    const daysText = daysDifference < 0 ? daysDifference * -1 + \" days ago\" : \"Due - \" + daysDifference + \" days\";\n    return {\n        class: daysDifference < 0 ? classes.high : classes.low,\n        label: daysText,\n        isOverdue: daysDifference < 0,\n        dueWithinSevenDays: !(daysDifference < 0) && daysDifference <= 7\n    };\n};\n_c1 = GetTrainingSessionStatus;\nasync function getLogBookByID(id, setLogBook) {\n    _s36();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryLogBook] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_LOGBOOK, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBook;\n            if (data) {\n                setLogBook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBook error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadLogBook();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadLogBook = async ()=>{\n        await queryLogBook({\n            variables: {\n                id: +id\n            }\n        });\n    };\n}\n_s36(getLogBookByID, \"uSobPTULaJxj1+5MUJMKWpRO/x0=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getLogBookEntryByID(id, setLogBookEntry) {\n    _s37();\n    const online = true // To be replaced with useOnline()\n    ;\n    const lbeModel = new _offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_7__[\"default\"]();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                setLogBookEntry(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadLogBookEntry();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadLogBookEntry = async ()=>{\n        if (online) {\n            await queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        } else {\n            const response = await lbeModel.getById(id);\n            setLogBookEntry(response);\n        }\n    };\n}\n_s37(getLogBookEntryByID, \"wlJ+aRF3MWp0exkapQJWxvBJUzE=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getOneClient(setClient) {\n    let offline = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    _s38();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const clientModel = new _offline_models_client__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const [queryClientByID] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.READ_ONE_CLIENT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneClient;\n            if (data) {\n                setClient(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryClientByID error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadClientByID();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadClientByID = async ()=>{\n        if (offline) {\n            var _localStorage_getItem;\n            const c = await clientModel.getById((_localStorage_getItem = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : 0);\n            setClient(c);\n        } else {\n            var _localStorage_getItem1;\n            await queryClientByID({\n                variables: {\n                    filter: {\n                        id: {\n                            eq: +((_localStorage_getItem1 = localStorage.getItem(\"clientId\")) !== null && _localStorage_getItem1 !== void 0 ? _localStorage_getItem1 : 0)\n                        }\n                    }\n                }\n            });\n        }\n    };\n}\n_s38(getOneClient, \"Ku2fHZTGejxnpy/9DG6CpYWu9xY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getCrewMembersLogBookEntrySections(crewMemberID, setLogBookEntrySections) {\n    _s39();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryLogBookEntrySections] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewMembers_LogBookEntrySections.nodes;\n            if (data) {\n                setLogBookEntrySections(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadLogBookEntrySections();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadLogBookEntrySections = async ()=>{\n        const searchFilter = {};\n        searchFilter.crewMemberID = {\n            eq: crewMemberID\n        };\n        await queryLogBookEntrySections({\n            variables: {\n                filter: searchFilter\n            }\n        });\n    };\n}\n_s39(getCrewMembersLogBookEntrySections, \"zG4eArFCkK26w6k7CEMzAvcnECk=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst GetCrewListWithTrainingStatus = (crewList, vessels)=>{\n    const vesselID = vessels.length === 1 ? +vessels[0].id : 0;\n    const updatedCrewList = crewList.map((crewMember)=>{\n        if (crewMember.hasTrainingSessionDue && crewMember.trainingSessionsDue && crewMember.trainingSessionsDue.nodes) {\n            return {\n                ...crewMember,\n                trainingSessionsDue: {\n                    ...crewMember.trainingSessionsDue,\n                    nodes: crewMember.trainingSessionsDue.nodes.filter((node)=>{\n                        return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate);\n                    }).map((node)=>({\n                            ...node,\n                            status: GetTrainingSessionStatus(node)\n                        }))\n                }\n            };\n        }\n        return crewMember;\n    });\n    const crewListWithTrainingStatus = updatedCrewList.map((crewMember)=>{\n        if (crewMember.trainingSessionsDue && crewMember.trainingSessionsDue.nodes) {\n            const filteredNodes = crewMember.trainingSessionsDue.nodes.filter((node)=>{\n                if (vesselID > 0) {\n                    return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate) && +node.vesselID === +vesselID;\n                } else {\n                    return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate);\n                }\n            });\n            const mappedNodes = filteredNodes.map((node)=>({\n                    ...node,\n                    status: GetTrainingSessionStatus(node)\n                }));\n            let trainingStatus = {\n                label: \"Good\",\n                dues: []\n            };\n            if (filteredNodes.length === 0) {\n                trainingStatus = {\n                    label: \"Good\",\n                    dues: []\n                };\n            } else {\n                // Check for overdue and due sessions separately\n                const overdueSessions = filteredNodes.filter((node)=>{\n                    var _node_status;\n                    return (_node_status = node.status) === null || _node_status === void 0 ? void 0 : _node_status.isOverdue;\n                });\n                const dueSessions = filteredNodes.filter((node)=>{\n                    var _node_status;\n                    return (_node_status = node.status) === null || _node_status === void 0 ? void 0 : _node_status.dueWithinSevenDays;\n                });\n                if (overdueSessions.length > 0 && dueSessions.length > 0) {\n                    // Both overdue and due sessions exist - neutral status (Good)\n                    trainingStatus = {\n                        label: \"Good\",\n                        dues: [\n                            ...overdueSessions,\n                            ...dueSessions\n                        ]\n                    };\n                } else if (overdueSessions.length > 0) {\n                    // Only overdue sessions exist\n                    trainingStatus = {\n                        label: \"Overdue\",\n                        dues: overdueSessions\n                    };\n                } else if (dueSessions.length > 0) {\n                    // Only due sessions exist\n                    trainingStatus = {\n                        label: \" \",\n                        dues: dueSessions\n                    };\n                } else {\n                    // No overdue or due sessions\n                    trainingStatus = {\n                        label: \"Good\",\n                        dues: []\n                    };\n                }\n            }\n            // Remove duplicate objects based on trainingTypeID and vesselID\n            const uniqueDues = Object.values(trainingStatus.dues.reduce((acc, due)=>{\n                const key = due.trainingTypeID;\n                if (!acc[key]) {\n                    acc[key] = due;\n                }\n                return acc;\n            }, {}));\n            return {\n                ...crewMember,\n                trainingSessionsDue: {\n                    ...crewMember.trainingSessionsDue,\n                    nodes: mappedNodes\n                },\n                trainingStatus: {\n                    ...trainingStatus,\n                    dues: uniqueDues\n                }\n            };\n        }\n        return {\n            ...crewMember,\n            trainingStatus: {\n                label: \"Good\",\n                dues: []\n            }\n        };\n    });\n    return crewListWithTrainingStatus;\n};\n_c2 = GetCrewListWithTrainingStatus;\nconst GetVesselWithTaskStatus = (vessels)=>{\n    /*\r\n    const daysDifference = Math.ceil(\r\n        (new Date(task.expires).getTime() - today.getTime()) /\r\n            (1000 * 60 * 60 * 24),\r\n    )\r\n    const isOverdue = daysDifference < 0\r\n    const dueWithinSevenDays = !(daysDifference < 0) && daysDifference <= 7 */ const vesselList = vessels.map((vessel)=>{\n        let taskStatus = {\n            label: \"Good\",\n            tasks: []\n        };\n        if (vessel.componentMaintenanceChecks && vessel.componentMaintenanceChecks.nodes) {\n            const today = new Date();\n            let overdueTasks = [];\n            let upcomingTasks = [];\n            vessel.componentMaintenanceChecks.nodes.forEach((task)=>{\n                const daysDifference = Math.ceil((new Date(task.expires).getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n                const isOverdue = daysDifference < 0;\n                const dueWithinSevenDays = !(daysDifference < 0) && daysDifference <= 7;\n                if (isOverdue) {\n                    overdueTasks.push({\n                        ...task,\n                        isOverdue: isOverDueTask(task)\n                    });\n                }\n                if (dueWithinSevenDays) {\n                    upcomingTasks.push({\n                        ...task,\n                        isOverdue: isOverDueTask(task)\n                    });\n                }\n            });\n            if (overdueTasks.length > 0) {\n                taskStatus = {\n                    label: \"Overdue\",\n                    tasks: overdueTasks\n                };\n            }\n            if (upcomingTasks.length > 0) {\n                taskStatus = {\n                    label: \"Upcoming\",\n                    tasks: upcomingTasks\n                };\n            }\n        }\n        // const componentMaintenanceChecks =\n        //     vessel.componentMaintenanceChecks.nodes.map((task: any) => {\n        //         return {\n        //             ...task,\n        //             isOverDue: isOverDueTask(task),\n        //         }\n        //     })\n        return {\n            ...vessel,\n            taskStatus: taskStatus\n        };\n    });\n    return vesselList;\n};\n_c3 = GetVesselWithTaskStatus;\nconst GetVesselWithTrainingStatus = (vessels)=>{\n    return vessels.map((vessel)=>{\n        if (vessel.trainingSessionsDue && vessel.trainingSessionsDue.nodes) {\n            const vesselID = +vessel.id;\n            const filteredNodes = vessel.trainingSessionsDue.nodes.filter((node)=>{\n                if (vesselID > 0) {\n                    return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate) && +node.vesselID === +vesselID;\n                } else {\n                    return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate);\n                }\n            });\n            const mappedNodes = filteredNodes.map((node)=>({\n                    ...node,\n                    status: GetTrainingSessionStatus(node)\n                }));\n            let trainingStatus = {\n                label: \"Good\",\n                dues: []\n            };\n            if (filteredNodes.length === 0) {\n                trainingStatus = {\n                    label: \"Good\",\n                    dues: []\n                };\n            } else if (filteredNodes.some((node)=>node.status.dueWithinSevenDays)) {\n                trainingStatus = {\n                    label: \"Upcoming\",\n                    dues: filteredNodes.filter((node)=>node.status.dueWithinSevenDays)\n                };\n            } else if (filteredNodes.some((node)=>node.status.isOverdue)) {\n                trainingStatus = {\n                    label: \"Overdue\",\n                    dues: filteredNodes.filter((node)=>node.status.isOverdue)\n                };\n            }\n            // Remove duplicate objects based on trainingTypeID and vesselID\n            const uniqueDues = Object.values(trainingStatus.dues.reduce((acc, due)=>{\n                const key = due.trainingTypeID;\n                if (!acc[key]) {\n                    acc[key] = due;\n                }\n                return acc;\n            }, {}));\n            return {\n                ...vessel,\n                trainingSessionsDue: {\n                    ...vessel.trainingSessionsDue,\n                    nodes: mappedNodes\n                },\n                trainingStatus: {\n                    ...trainingStatus,\n                    dues: uniqueDues\n                }\n            };\n        }\n        return {\n            ...vessel,\n            trainingStatus: {\n                label: \"Good\",\n                dues: []\n            }\n        };\n    });\n};\n_c4 = GetVesselWithTrainingStatus;\nconst GetVesselListWithTrainingAndMaintenanceStatus = (vesselList)=>{\n    const updatedVesselList = vesselList.map((vessel)=>{\n        if (vessel.trainingSessionsDue && vessel.trainingSessionsDue.nodes) {\n            return {\n                ...vessel,\n                // trainingStatus: GetVesselTrainingStatus(vessel),\n                trainingSessionsDue: {\n                    ...vessel.trainingSessionsDue,\n                    nodes: vessel.trainingSessionsDue.nodes.filter((node)=>{\n                        return !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(node.dueDate);\n                    }).map((node)=>({\n                            ...node,\n                            status: GetTrainingSessionStatus(node)\n                        }))\n                }\n            };\n        }\n        return vessel;\n    });\n    const vesselListWithTrainingStatus = GetVesselWithTrainingStatus(updatedVesselList);\n    const vesselListWithMaintenanceStatus = GetVesselWithTaskStatus(vesselListWithTrainingStatus);\n    return vesselListWithMaintenanceStatus;\n};\n_c5 = GetVesselListWithTrainingAndMaintenanceStatus;\nasync function getSectionMemberComments(sectionID, setSectionMemberComments) {\n    _s40();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySectionMemberComments] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_SECTION_MEMBER_COMMENTS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSectionMemberComments.nodes;\n            if (data) {\n                setSectionMemberComments(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySectionMemberComments error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadSectionMemberComments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadSectionMemberComments = async ()=>{\n        await querySectionMemberComments({\n            variables: {\n                filter: {\n                    logBookEntrySectionID: {\n                        eq: sectionID\n                    }\n                }\n            }\n        });\n    };\n}\n_s40(getSectionMemberComments, \"u69m2fqSv4VtI1slBrMcbbQW0iY=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst getFieldName = (field)=>{\n    if ((field === null || field === void 0 ? void 0 : field.__typename) && field.__typename === \"CustomisedComponentField\") {\n        const logbookFields = _logbook_configuration__WEBPACK_IMPORTED_MODULE_4__.SLALL_LogBookFields;\n        const defaultConfig = logbookFields.map((component)=>component);\n        var title = field.fieldName;\n        defaultConfig.forEach((defaultLogBookComponents)=>{\n            defaultLogBookComponents.items.forEach((defaultField)=>{\n                if (field.fieldName === defaultField.value) {\n                    title = (defaultField === null || defaultField === void 0 ? void 0 : defaultField.title) ? defaultField === null || defaultField === void 0 ? void 0 : defaultField.title : field.fieldName;\n                }\n            });\n        });\n        return title;\n    } else {\n        return (field === null || field === void 0 ? void 0 : field.title) ? field.title : field.value;\n    }\n};\nasync function getDepartmentList(setDepartmentList) {\n    _s41();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [queryDepartmentList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                setDepartmentList(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryDepartmentList error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadDepartments = async ()=>{\n        await queryDepartmentList();\n    };\n}\n_s41(getDepartmentList, \"X8jVq1v8m9jnA/gs3U3z5XG8Lbo=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nasync function getSeaTimeReport(crewMemberIds, vesselIds, startDate, endDate, setSeaTimeReport) {\n    _s42();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [querySeaTimeReport] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.GET_SEA_TIME_REPORT, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const reportData = response.seaTimeReport;\n            if (reportData) {\n                setSeaTimeReport(reportData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"querySeaTimeReport error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadSeaTimeReport();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const loadSeaTimeReport = async ()=>{\n        await querySeaTimeReport({\n            variables: {\n                crewMemberIds,\n                vesselIds,\n                startDate,\n                endDate\n            }\n        });\n    };\n}\n_s42(getSeaTimeReport, \"lRLrgYh9orwA877F1+zdRxV62ys=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst convertTimeFormat = (time)=>{\n    if (time === null || time === undefined) return \"\";\n    const [hours, minutes, seconds] = time.split(\":\");\n    return \"\".concat(hours, \":\").concat(minutes);\n};\nconst userHasRescueVessel = (setHasRescueVessel)=>{\n    _s43();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadVessels();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_0__.VESSEL_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                setHasRescueVessel(queryVesselResponse.readVessels.nodes.filter((vessel)=>vessel.vesselType === \"Rescue_Vessel\").length > 0);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async ()=>{\n        await queryVessels({\n            variables: {\n                filter: {\n                    archived: {\n                        eq: false\n                    }\n                }\n            }\n        });\n    };\n};\n_s43(userHasRescueVessel, \"pQOK42e7v9ItR64U9CP/qx2cQME=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\nconst getSignatureUrl = async function(id) {\n    let setImage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    let url = \"\";\n    // Don't attempt to fetch signature if ID is 0 or invalid\n    if (!id || id <= 0) {\n        console.warn(\"Invalid signature ID: \".concat(id));\n        return url;\n    }\n    const fileName = \"signature/\" + id + \".png\";\n    try {\n        const data = await s3Client.getObject({\n            Bucket: \"signature\",\n            Key: fileName\n        }).promise();\n        if (data.Body) {\n            const base64Data = Buffer.from(data.Body).toString(\"base64\");\n            const imageUrl = \"data:image/png;base64,\".concat(base64Data);\n            url = imageUrl;\n            if (setImage) {\n                setImage(imageUrl);\n            }\n        }\n    } catch (err) {\n        console.error(\"Error fetching signature \".concat(id, \":\"), err);\n    }\n    return url;\n};\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"GetLogBookEntriesMembers\");\n$RefreshReg$(_c1, \"GetTrainingSessionStatus\");\n$RefreshReg$(_c2, \"GetCrewListWithTrainingStatus\");\n$RefreshReg$(_c3, \"GetVesselWithTaskStatus\");\n$RefreshReg$(_c4, \"GetVesselWithTrainingStatus\");\n$RefreshReg$(_c5, \"GetVesselListWithTrainingAndMaintenanceStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/actions.tsx\n"));

/***/ })

});