"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/weather/forecast.tsx":
/*!*****************************************!*\
  !*** ./src/app/ui/weather/forecast.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _logbook_components_location_location__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logbook/components/location/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location/location.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/weatherHelper */ \"(app-pages-browser)/./src/app/helpers/weatherHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _forecast_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forecast-list */ \"(app-pages-browser)/./src/app/ui/weather/forecast-list.tsx\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/weatherForecast */ \"(app-pages-browser)/./src/app/offline/models/weatherForecast.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Clock,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./widgets/wind-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/wind-widget.tsx\");\n/* harmony import */ var _widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./widgets/swell-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/swell-widget.tsx\");\n/* harmony import */ var _widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./widgets/cloud-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/cloud-widget.tsx\");\n/* harmony import */ var _widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./widgets/barometer-widget */ \"(app-pages-browser)/./src/app/ui/weather/widgets/barometer-widget.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst WeatherForecast = (param)=>{\n    let { logBookEntryID, offline = false, locked, forecast, setForecast, isWriteModeForecast, setIsWriteModeForecast } = param;\n    _s();\n    const [isManualEntry, setIsManualEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Current location state for the location field\n    const [currentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const [selectedCoordinates, setSelectedCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: null,\n        longitude: null\n    });\n    const isOnline = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline)();\n    const forecastModel = new _app_offline_models_weatherForecast__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const [isStormGlassLoading, setIsStormGlassLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshList, setRefreshList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [geoLocations, setGeoLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getTimeNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"HH:mm\");\n    };\n    const [getGeoLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_12__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readGeoLocations.nodes;\n            if (data) {\n                setGeoLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryGeoLocations error\", error);\n        }\n    });\n    const getDayNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\");\n    };\n    const initForecast = ()=>{\n        setForecast({\n            id: 0,\n            time: getTimeNow(),\n            day: getDayNow(),\n            geoLocationID: 0,\n            lat: 0,\n            long: 0,\n            logBookEntryID: logBookEntryID\n        });\n    };\n    const createForecast = ()=>{\n        initForecast();\n        setIsWriteModeForecast(true);\n    };\n    const handleSetCurrentLocation = (value)=>{\n        setForecast({\n            ...forecast,\n            geoLocationID: 0,\n            lat: value.latitude,\n            long: value.longitude\n        });\n        setSelectedCoordinates({\n            latitude: value.latitude,\n            longitude: value.longitude\n        });\n    };\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setForecast({\n                ...forecast,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, use them directly\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setSelectedCoordinates({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            } else {\n                // Otherwise find the location in geoLocations by ID\n                geoLocations.find((item)=>{\n                    if (item.id == +value.value) {\n                        setSelectedCoordinates({\n                            latitude: item.lat,\n                            longitude: item.long\n                        });\n                        return true;\n                    }\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setForecast({\n                ...forecast,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update selected coordinates\n            setSelectedCoordinates({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    };\n    const processStormGlassData = (data)=>{\n        const { windSpeed, windDirection, swellHeight, visibility, precipitation, pressure, cloudCover } = data.hours[0];\n        const windSpeedInKnots = (windSpeed ? (windSpeed.noaa || windSpeed.sg || 0) / 0.51444 : 0).toFixed(0) // Convert m/s to knot. One knot is equal to approximately 0.51444 meters per second (m/s).\n        ;\n        const compassWindDirection = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getWindDirection)(windDirection ? windDirection.noaa || windDirection.sg || 0 : 0) // convert degrees to compass direction\n        ;\n        const swellValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getSwellHeightRange)(swellHeight ? swellHeight.noaa || swellHeight.sg || 0 : 0);\n        const visibilityValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getVisibility)(visibility ? visibility.noaa || visibility.sg || 0 : 0);\n        const precipitationValue = (0,_app_helpers_weatherHelper__WEBPACK_IMPORTED_MODULE_6__.getPrecipitation)(precipitation ? precipitation.noaa || precipitation.sg || 0 : 0);\n        const pressureValue = pressure ? pressure.noaa || pressure.sg || 0 : 0;\n        const cloudCoverValue = (cloudCover ? cloudCover.noaa || cloudCover.sg || 0 : 0).toFixed(0);\n        setForecast({\n            ...forecast,\n            windSpeed: +windSpeedInKnots,\n            windDirection: compassWindDirection,\n            swell: swellValue,\n            visibility: visibilityValue,\n            precipitation: precipitationValue,\n            pressure: +pressureValue,\n            cloudCover: +cloudCoverValue\n        });\n        setIsStormGlassLoading(false);\n    };\n    const isStormGlassButtonEnabled = ()=>{\n        let isStormGlassButtonEnabled = false;\n        if (+forecast.geoLocationID > 0) {\n            isStormGlassButtonEnabled = true;\n        } else if (!isNaN(+forecast.lat) || !isNaN(+forecast.long)) {\n            isStormGlassButtonEnabled = true;\n        }\n        if (!isOnline) {\n            isStormGlassButtonEnabled = false;\n        }\n        return isStormGlassButtonEnabled;\n    };\n    const getStormGlassData = ()=>{\n        setIsManualEntry(false);\n        if (forecast.geoLocationID > 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.dismiss();\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n            setIsStormGlassLoading(true);\n            const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n            let startDate = new Date(dateString);\n            let endDate = startDate;\n            var headers = {\n                \"Cache-Control\": \"no-cache\",\n                Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                \"Access-Control-Allow-Credentials\": \"true\"\n            };\n            var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n            const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n            let request = fetch(url, {\n                method: \"GET\",\n                headers\n            });\n            request.then((response)=>response.json()).then((jsonData)=>{\n                // toast.dismiss()\n                // toast.success('Forecast retrieved successfully')\n                processStormGlassData(jsonData);\n            }).catch((error)=>{\n                setIsStormGlassLoading(false);\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                console.error(\"Catch error:\", error);\n            });\n            return request;\n        } else {\n            if (\"geolocation\" in navigator) {\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.dismiss();\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.loading(\"Retrieving forecast...\");\n                setIsStormGlassLoading(true);\n                return new Promise((resolve, reject)=>{\n                    return navigator.geolocation.getCurrentPosition(()=>{\n                        const dateString = \"\".concat(forecast.day, \" \").concat(forecast.time);\n                        let startDate = new Date(dateString);\n                        let endDate = startDate;\n                        var headers = {\n                            \"Cache-Control\": \"no-cache\",\n                            Authorization: \"480c5714-38bc-11ea-acb4-0242ac130002-480c58fe-38bc-11ea-acb4-0242ac130002\" || 0,\n                            \"Access-Control-Allow-Credentials\": \"true\"\n                        };\n                        var params = \"windSpeed,windDirection,swellHeight,visibility,precipitation,pressure,cloudCover\";\n                        const url = \"https://api.stormglass.io/v2/weather/point?lat=\".concat(selectedCoordinates.latitude || 0, \"&lng=\").concat(selectedCoordinates.longitude || 0, \"&params=\").concat(params, \"&start=\").concat(startDate.toISOString(), \"&end=\").concat(endDate.toISOString());\n                        let request = fetch(url, {\n                            method: \"GET\",\n                            headers\n                        });\n                        request.then((response)=>response.json()).then((jsonData)=>{\n                            // toast.dismiss()\n                            // toast.success(\n                            //     'Forecast retrieved successfully',\n                            // )\n                            processStormGlassData(jsonData);\n                            resolve(jsonData);\n                        }).catch((error)=>{\n                            setIsStormGlassLoading(false);\n                            reject(error);\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.dismiss();\n                            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                            console.error(\"Catch error:\", error);\n                        });\n                        return request;\n                    }, (error)=>{\n                        setIsStormGlassLoading(false);\n                        reject(error);\n                        sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"There was a problem retrieving the forecast. Please try again later.\");\n                        console.error(\"Geolocation error\", error);\n                    });\n                });\n            } else {\n                setIsStormGlassLoading(false);\n                console.error(\"Geolocation is not supported by your browser\");\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"Geolocation is not supported by your browser\");\n            }\n        }\n    };\n    const handleOnChangePressure = (value)=>{\n        const pressure = Array.isArray(value) ? value[0] : value;\n        setForecast({\n            ...forecast,\n            pressure: pressure\n        });\n    };\n    const handleOnChangeSeaSwell = (item)=>{\n        if (item) {\n            setForecast({\n                ...forecast,\n                swell: item.value\n            });\n        }\n    };\n    const handleWindChange = (values)=>{\n        // Update both wind speed and direction in the forecast state\n        setForecast({\n            ...forecast,\n            windSpeed: values.speed,\n            windDirection: values.direction.value\n        });\n    };\n    const handleSetComment = lodash_debounce__WEBPACK_IMPORTED_MODULE_5___default()((item)=>{\n        setForecast({\n            ...forecast,\n            comment: item\n        });\n    }, 600);\n    const [createWeatherForecast, { loading: createWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.CreateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"CreateWeatherForecast Error\", error);\n        }\n    });\n    const [updateWeatherForecast, { loading: updateWeatherForecastLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.UpdateWeatherForecast, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"UpdateWeatherForecast Error\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        if (+forecast.id === 0) {\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)()\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await createWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        } else {\n            if (forecast.geoLocation) delete forecast.geoLocation;\n            if (forecast.__typename) delete forecast.__typename;\n            if (offline) {\n                await forecastModel.save({\n                    ...forecast,\n                    day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day).format(\"YYYY-MM-DD\")\n                });\n                setIsWriteModeForecast(false);\n                setRefreshList(true);\n            } else {\n                await updateWeatherForecast({\n                    variables: {\n                        input: {\n                            ...forecast\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const handleCancel = ()=>{\n        initForecast();\n        setIsWriteModeForecast(false);\n    };\n    const handleForecastClick = (forecast)=>{\n        if (locked) {\n            return;\n        }\n        setIsManualEntry(false);\n        const newForecast = {\n            ...forecast,\n            time: formatTime(forecast.time),\n            day: dayjs__WEBPACK_IMPORTED_MODULE_2___default()(forecast.day.toString()).format(\"YYYY-MM-DD\")\n        };\n        setForecast(newForecast);\n        setIsWriteModeForecast(true);\n    };\n    const [deleteWeatherForecasts, { loading: deleteWeatherForecastsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_7__.DeleteWeatherForecasts, {\n        onCompleted: ()=>{\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        },\n        onError: (error)=>{\n            console.error(\"DeleteWeatherForecasts Error\", error);\n        }\n    });\n    const handleDeleteForecast = async ()=>{\n        if (offline) {\n            forecastModel.delete(forecast);\n            setIsWriteModeForecast(false);\n            setRefreshList(true);\n        } else {\n            await deleteWeatherForecasts({\n                variables: {\n                    ids: [\n                        forecast.id\n                    ]\n                }\n            });\n        }\n    };\n    const formatTime = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).format(\"HH:mm\");\n    };\n    const handleOnChangeCloudWidget = (item)=>{\n        setForecast({\n            ...forecast,\n            visibility: item.visibility.value,\n            precipitation: item.precipitation.value,\n            cloudCover: item.cloudCover\n        });\n    };\n    // Format time string for display\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logBookEntryID > 0) {\n            setForecast({\n                ...forecast,\n                logBookEntryID: logBookEntryID\n            });\n        }\n        getGeoLocations();\n    }, [\n        logBookEntryID\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_18__.Card, {\n        children: [\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"mb-4 max-w-\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"You can start by retrieving a weather forecast up to 7-days into the future. SeaLogs currently using the Stormglass API with more forecasting services coming soon.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_17__.P, {\n                                children: \"After retrieving a forecast you can add your own observations. We use this data to compare the accuracy of forecasts plus share weather observations with our community of users.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                            disabled: locked,\n                            onClick: ()=>createForecast(),\n                            children: \"Add another forecast\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true),\n            isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            id: \"forecast-date\",\n                                            name: \"forecast-date\",\n                                            label: \"Date and time of forecast required\",\n                                            value: forecast.day && forecast.time ? new Date(\"\".concat(forecast.day, \"T\").concat(forecast.time)) : undefined,\n                                            mode: \"single\",\n                                            type: \"datetime\" // Keep datetime to include time picker\n                                            ,\n                                            onChange: (date)=>{\n                                                if (date) {\n                                                    const newDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date);\n                                                    setForecast({\n                                                        ...forecast,\n                                                        day: newDate.format(\"YYYY-MM-DD\"),\n                                                        time: newDate.format(\"HH:mm:00\")\n                                                    });\n                                                }\n                                            },\n                                            dateFormat: \"dd MMM,\",\n                                            timeFormat: \"HH:mm\" // Explicitly set time format\n                                            ,\n                                            placeholder: \"Time\",\n                                            closeOnSelect: false,\n                                            clearable: true,\n                                            icon: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                            className: \"w-full\",\n                                            includeTime: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2 flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            label: \"Location for forecast\",\n                                            className: \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_logbook_components_location_location__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    offline: offline,\n                                                    setCurrentLocation: handleSetCurrentLocation,\n                                                    handleLocationChange: (e)=>{\n                                                        handleLocationChange(e);\n                                                    },\n                                                    currentEvent: {\n                                                        geoLocationID: forecast.geoLocationID,\n                                                        lat: forecast.lat,\n                                                        long: forecast.long\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    isWriteModeForecast && isStormGlassButtonEnabled() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            onClick: ()=>getStormGlassData(),\n                                            disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                            className: \"w-full\",\n                                            children: isStormGlassLoading ? \"Retrieving forecast...\" : \"Retrieve forecast (API)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                            variant: \"secondary\",\n                                            onClick: ()=>setIsManualEntry(true),\n                                            className: \"w-full\",\n                                            children: \"OR enter a manual forecast (observation)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: isManualEntry ? \"flex flex-col gap-4\" : \"grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_wind_widget__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        speed: forecast.windSpeed,\n                                        direction: forecast.windDirection,\n                                        onChange: handleWindChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_cloud_widget__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        visibilityValue: forecast.visibility,\n                                        precipitationValue: forecast.precipitation,\n                                        cloudCoverValue: forecast.cloudCover,\n                                        onChange: handleOnChangeCloudWidget\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_swell_widget__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        editMode: isManualEntry,\n                                        value: forecast.swell,\n                                        onChange: handleOnChangeSeaSwell\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_widgets_barometer_widget__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        value: forecast.pressure,\n                                        editMode: isManualEntry,\n                                        onChange: handleOnChangePressure\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 25\n                            }, undefined),\n                            !isManualEntry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-[10px]\",\n                                children: \"Forecast provided by Stormglass API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    label: \"Your comments\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_15__.Textarea, {\n                                        id: \"forecast-comment\",\n                                        rows: 4,\n                                        placeholder: \"Comments ...\",\n                                        defaultValue: forecast.comment || \"\",\n                                        onChange: (e)=>handleSetComment(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"flex flex-col-reverse small:flex-row gap-2.5 small:justify-end mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"back\",\n                                        className: \"w-full small:w-fit\",\n                                        onClick: handleCancel,\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    +forecast.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        variant: \"destructive\",\n                                        className: \"w-full small:w-fit\",\n                                        iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                        onClick: ()=>{\n                                            setDeleteDialogOpen(true);\n                                        },\n                                        disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                        children: \"Delete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                iconLeft: _barrel_optimize_names_Check_Clock_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                onClick: handleSave,\n                                disabled: isStormGlassLoading || createWeatherForecastLoading || updateWeatherForecastLoading || deleteWeatherForecastsLoading,\n                                children: \"\".concat(+forecast.id === 0 ? \"Save\" : \"Update\", \" forecast\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 529,\n                columnNumber: 17\n            }, undefined),\n            !isWriteModeForecast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forecast_list__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    logBookEntryID: logBookEntryID,\n                    refreshList: refreshList,\n                    onClick: handleForecastClick\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 727,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_14__.AlertDialogNew, {\n                openDialog: deleteDialogOpen,\n                setOpenDialog: setDeleteDialogOpen,\n                handleCreate: handleDeleteForecast,\n                title: \"Delete Forecast Data\",\n                variant: \"warning\",\n                actionText: \"Delete\",\n                cancelText: \"Cancel\",\n                children: [\n                    \"Are you sure you want to delete the forecast data for\",\n                    forecast.time ? formatTime(forecast.time) : \"\",\n                    \" /\",\n                    forecast.day ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_11__.formatDate)(forecast.day) : \"\",\n                    \"?\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n                lineNumber: 737,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\weather\\\\forecast.tsx\",\n        lineNumber: 502,\n        columnNumber: 9\n    }, undefined);\n};\n_s(WeatherForecast, \"3b9KgFxO9G18as6x7Qm9IhXaZNA=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_24__.useOnline,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useMutation\n    ];\n});\n_c = WeatherForecast;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WeatherForecast);\nvar _c;\n$RefreshReg$(_c, \"WeatherForecast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/weather/forecast.tsx\n"));

/***/ })

});